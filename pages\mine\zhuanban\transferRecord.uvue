<template>
	<view class="status_bar">
		
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
		<view class="title_content" @click="handelSelect">
			<text class="title">{{title}}</text>
			<text class="iconfont xiala_icon">&#xe600;</text>
		</view>
	
	    <view class="nav-btn" >
			<!-- <text class="iconfont icon_font" v-if="isEditOrDetails=='2'">&#xe7e7;</text> -->
	    </view>
	</view>
	<view class="tabs-container" >
		<view class="tabs-header">
			<view
				class="tab-item"
				:class="{ 'tab-active': currentTab === index }"
				v-for="(tab, index) in tabList"
				:key="index"
				@tap="switchTab(index)"
			>
				<text class="tab-text" :class="{ 'tab-text-active': currentTab === index }">{{ tab.name }}</text>
				<view class="tab-indicator" v-if="currentTab === index"></view>
			</view>
		</view>
	</view>
	<scrollRefresh  :isRefreshing="refreshing"
	  :isLoading="loading"
	  :hasMore="hasMore"
	  @refresh="onRefreshTriggered"
	  @loadMore="onLoadMoreTriggered">
	  
		<view class="content_bj" >
			<view class="zbInfo" @click="handleDetails(item)" v-for="(item,index) in listData" :key="index">
				<view class="form_view" v-if="title_id=='1'">
					<text class="form_label">转办人：</text>
					<text class="form_content">王立文</text>
				</view>
				<view class="form_view" v-if="title_id=='2'">
					<text class="form_label">接收人：</text>
					<text class="form_content">王立文</text>
				</view>
				<view class="form_view">
					<text class="form_label">清单名称：</text>
					<text class="form_content">2025年2月13日清单</text>
				</view>
				<view class="form_view">
					<text class="form_label">开始时间：</text>
					<text class="form_content">2025年2月13日</text>
				</view>
				<view class="form_view">
					<text class="form_label">结束时间：</text>
					<text class="form_content">2025年2月13日</text>
				</view>
				
				<view class="zhuangtai" :style="{'background':item.status=='1'?'#007aff33':(item.status=='2'?'#38e32e36':'#e3343436')}">
					<text v-if="item.status=='1'" class="status_wz dcl">待处理</text>
					<text v-else-if="item.status=='2'" class="status_wz yjs">已接收</text>
					<text v-else-if="item.status=='3'" class="status_wz ybh">已驳回</text>
				</view>
			</view>
		</view>
	  
	  
	</scrollRefresh>
	
	
	<selectPicker v-if="showPicker"  :show="showPicker" @close="pickerColse" @confirm="pickerVonfirm" :items="titleList" :value="title_id"></selectPicker>
</template>

<script setup>
	import {ProjectOption,confirmHcType,pageType,zbTabs} from '@/utils/apiType.uts'
	import selectPicker from '@/components/select_picker_gj.uvue'
	import scrollRefresh from '@/components/scroll-refresh.uvue'
	
	
	// 定义数据结构
	type ListItem = {
	  id: number
	  status:string
	}
	
	
	const title=ref<string>('我发起的')
	// 选中的id
	const title_id=ref<string|number|null>('1')
	const showPicker=ref<boolean>(false)
	const selectActive=ref<ProjectOption|null>(null)

	// tabs相关数据
	const currentTab = ref<number>(0)
	const tabList = ref<zbTabs[]>([
		{ name: '全部', id: 0 },
		{ name: '待查看', id: 1 },
		{ name: '已接收', id: 2 },
		{ name: '已驳回', id: 3 }
	])
	
	// 选择的数据
	const titleList =ref<ProjectOption[]>([
		
		{value:'1',label:'我发起的'},
		{value:'2',label:'转办于我'},
	])
	
	const refreshing = ref(false)
	const loading = ref(false)
	const hasMore = ref(true)
	const listData = ref<ListItem[]>([]) // 您的数据
	// 分页
	const pageInfo=ref<pageType>({
		page:1,
		pageSize:15,
		total:20
	})
	
	// 加载数据
	const fetchData = () => {
		// console.log(1231231);
		if (loading.value || !hasMore.value) return
		loading.value=true
		setTimeout(() => {
			const newData: ListItem[] = []
			for (let i = 0; i < pageInfo.value.pageSize; i++) {
				 const id = (pageInfo.value.page - 1) * pageInfo.value.pageSize+ i + 1
				 newData.push({
				   id: id,
				   status:'1'
				 })
			}
			listData.value = pageInfo.value.page === 1 
				 ? newData 
				 : [...listData.value, ...newData]
			
			// 数据结束条件（可调整或移除）
			if(listData.value.length>=pageInfo.value.total){
				hasMore.value = false
			}
			// if (pageInfo.value.page >= 5) hasMore.value = false
			
			loading.value = false
			refreshing.value = false
			pageInfo.value.page++
			
		}, 800)
	}
	
	
	// 下拉刷新事件
	const onRefreshTriggered = () => {
	  if (refreshing.value) return
	  
	  refreshing.value = true
	  pageInfo.value.page = 1
	  hasMore.value = true
	  pageInfo.value.pageSize = 15 // 重置为初始值
	  
	  setTimeout(() => {
	    fetchData()
	  }, 1000)
	}
	
	// 上拉加载更多事件
	const onLoadMoreTriggered = () => {
		 if (!hasMore.value || loading.value) return
		 
		 // 每次加载固定数量 (无需修改pageSize)
		 pageInfo.value.pageSize = 15
		 fetchData()
	}
	
	// 打开弹窗组件
	const handelSelect=()=>{
		showPicker.value=true
	}
	// 关闭选择组件
	const pickerColse=()=>{
		showPicker.value=false
	}
	// 回传关闭组件
	const pickerVonfirm=(data:confirmHcType)=>{
		console.log('data',data);
		
		if(data.selectedItem!=null){
			const label=data.selectedItem['label']
			if (label != null) {
			  title.value = label as string
			}
			const value=data.selectedItem['value']
			if (value != null) {
			  title_id.value = value as string
			}
		}
		pageInfo.value.page=1
		currentTab.value=0
		listData.value=[]
		onRefreshTriggered()
		showPicker.value=false
	
	}
	
	
	
	// 切换tabs
	const switchTab = (index: number) => {
		currentTab.value = index
		// 这里可以根据不同的tab加载不同的数据
		console.log('切换到tab:', tabList.value[index].name)
		// 可以在这里调用不同的数据加载方法
		// loadTabData(index)
		pageInfo.value.page=1
		listData.value=[]
		onRefreshTriggered()
	}

	// 转办跳转详情
	const handleDetails=(item:ListItem)=>{
		uni.navigateTo({
			url:'/pages/mine/zhuanban/details?id='+item.id
		})
	}

	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	// 页面加载时初始化数据
	onLoad((options: OnLoadOptions) => {
		
		fetchData()
	})
</script>

<style lang="scss" scoped>
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  // height: 88px;
  padding: 30rpx 32rpx;
  background-color: #FFFFFF;
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE;
}

/* 修复2：滚动区域高度设置 */
.content-scroll {
  flex: 1; 
  width: 100%;
  // height: 1px;
  /* 确保内容不被导航栏遮挡 */
  box-sizing: border-box;
}

/* 修复3：添加内容项样式 */
.content-item {
  height: 200px;
  background: red;
  margin-bottom: 10px;
}

/* 其他原有样式保持不变 */
.title_content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 50%;
}

.title {
  font-size: 36rpx;
  color: #131313;
}

.icon_font {
  font-size: 32rpx;
  color: #000000;
}

.xiala_icon {
  font-size: 32rpx;
  color: #CBCBCB;
}

/* tabs样式 */
.tabs-container {
  background: #FFFFFF;
  margin-bottom: 20rpx;
}

.tabs-header {
  display: flex;
  flex-direction: row;
  background: #FFFFFF;
  border-bottom: 1rpx solid #EEEEEE;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item:active {
  background: #F5F5F5;
}

.tab-text {
  font-size: 30rpx;
  color: #666666;
  transition: color 0.3s ease;
}

.tab-text-active {
  color: #007AFF;
  font-weight: 700;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 4rpx;
  background: #007AFF;
  border-radius: 2rpx;
}

.tabs-content {
  padding: 30rpx;
}

.tab-pane {
  min-height: 200rpx;
}

.tab-content-text {
  font-size: 28rpx;
  color: #333333;
  text-align: center;
  line-height: 200rpx;
}
.content_bj{
	background: #F7F8FA;
}
.zbInfo{
	background: #FFFFFF;
	box-shadow: 5rpx 11rpx 17rpx 1rpx rgba(208,223,241,0.23);
	border-radius: 14rpx;
	padding:30rpx 30rpx;
	width: 90%;
	margin: 0 auto;
	margin-bottom: 20rpx;
	position: relative;
	.form_view{
		width: 100%;
		display: flex;
		flex-direction: row;
		margin-bottom: 20rpx;
		
		.form_label{
			font-size: 28rpx;
			color: #8B8B8B;
		}
		.form_content{
			flex: 1;
			font-size: 28rpx;
			color: #131313;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			
		}
	}
	.zhuangtai{
		width: 100rpx;
		height: 100rpx;
		// border: 1rpx solid #707070;
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		border-radius: 100rpx;
		// background: #007aff33;
		.status_wz{
			font-size: 28rpx;
			line-height: 100rpx;
			text-align: center;
		}
		.dcl{
			color: #007aff;
		}
		.yjs{
			color: #33b62b;
		}
		.ybh{
			color: #DB0000;
		}
	}
	.dcl_f{
		background: #007aff33;
	}
	
	
}
</style>
