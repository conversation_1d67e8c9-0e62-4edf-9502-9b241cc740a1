<template>
	<scroll-view style="flex: 1;" enable-back-to-top="true">
	  <view class="login-container">
		   <image 
			   class="background-image" 
			   src="/static/image/bj.png"
			   mode="cover"
			   
			 />
			
			<view class="welcome-section">
			  <text class="greeting">您好，</text>
			  <text class="platform-name">欢迎进入鹓翔任务平台</text>
			</view>
		
			<view class="login_content">
			
			<view class="input-group" style="margin-top: 100rpx;">
				<view class="input_left">
					<text class="iconfont icon_font">&#xe616;</text>
					<text class="input-label">手机号</text>
				</view>
			
			  <input 
				v-model="logonInfo.username"
				class="input-field" 
				placeholder="请输入手机号" 
				placeholder-class="placeholder"
				type="number"
			  />
			</view>
			
			
			<view class="input-group">
			  <view class="input_left">
				  <text class="iconfont icon_font">&#xe617;</text>
				<text class="input-label">密码</text>
			  </view>
			  <input 
			  v-model="logonInfo.password"
				class="input-field" 
				placeholder="请输入密码" 
				placeholder-class="placeholder"
				password
			  />
			</view>
			<view class="password-container">
				<text class="forgot-password" @click="forgotPassword" >忘记密码?</text>
			</view>
			
			
			
			<button class="login-btn" @click="handleLogin">登录</button>
			
			
			<text class="register-link" @click="register">注册账号</text>
			
			<!-- <web-view src="http://************:81/" /> -->
			
		</view>

		<view class="popup">
			<project-selector :show="showProjectSelector" @close="showProjectSelector = false" @confirm="onProjectSelected" />
		</view>
		
		
	   
	  </view>
  	</scroll-view>
</template>

<script setup>
	import {login} from '@/common/api.uts'
	import {LoginData,ProjectOption} from '@/utils/apiType.uts'
	import {ref} from 'vue'
	import ProjectSelector from '@/components/ProjectSelector.uvue';

	const logonInfo=ref<LoginData>({
		username:'',
		password:'',
		clientId:'1428a8310cd442757ae699df5d894f051',
		grantType:'password',
	})
	
	const showProjectSelector = ref(false);
	
	const onProjectSelected = (selectedProject:ProjectOption) => {
	  console.log('选中的项目部:', selectedProject);
	};
	// 这里可以添加登录逻辑
	const handleLogin = () => {
		
	  // 登录逻辑实现
	 //  login(logonInfo.value).then(res => {
		// if(res.code === 200) {
		//   console.log(res);
		// }
	 //  })
	  showProjectSelector.value=true
	}
	// 忘记密码
	const forgotPassword=()=>{
		// console.log('忘记密码');
		uni.navigateTo({
			url:'/pages/login/editPassword'
		})
	}
	// 注册账号
	const register=()=>{
		console.log('注册');
		uni.navigateTo({
			url:'/pages/login/register'
		})
	}
</script>

<style lang="scss" scoped>

.login-container {
  display: flex;
  flex-direction: column;
  // padding: 40rpx;
  height: 100%;
  width: 100%;
  position: relative;
  // background-image: url('/static/image/bj.png');
  // background-repeat: no-repeat;
  // background-size: cover;
  // position: relative;
  
}
.background-image {
   width: 100%;
   height: 100%;
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   z-index: -1;
}

.welcome-section {
  padding: 40rpx 60rpx;
  margin: 120rpx 0 60rpx;
  // text-align: center;
}

.greeting {
  font-size: 50rpx;
  color: #FFFFFF;
  
}

.platform-name {
  font-size: 46rpx;
  color: #FFFFFF;
  margin-top: 10rpx;
  
}

.input-group {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 30rpx 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  width: 90%;
  background: #F4F7F9;
  border-radius: 52rpx 52rpx 52rpx 52rpx;
  // margin: 0 auto;
}
.input_left{
	display: flex;
	align-items: center;
	flex-direction: row;
	border-right: 1rpx solid #C3CED5;
	width: 30%;
	padding: 0 20rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
 

}


.input-field {
  font-size: 32rpx;
  margin-left: 10rpx;
   width: 70%;
}

.placeholder {
  color: #CCCCCC;
  font-size: 28rpx;
}

.forgot-password {
  width: 80%;
  font-size: 28rpx;
  color: #4A90E2;
  text-align: right;
  // display: flex;
  // justify-content: flex-end;
}

.login-btn {
  width: 85%;
  background-color: #0189F6;
  color: #FFFFFF;
  border-radius: 50rpx;
  margin-top: 40rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  box-shadow: 3rpx 7rpx 12rpx 1rpx rgba(1,137,246,0.24);
}

.register-link {
  text-align: center;
  font-size: 28rpx;
  color: #999999;
  margin-top: 30rpx;
}
.login_content{
	width: 100%;
	height: 80%;
	padding: 40rpx 20rpx;
	
	background: #FFFFFF;
	border-top-left-radius: 30rpx;
	border-top-right-radius: 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.icon_font{
	font-size: 30rpx;
	margin-top: 5rpx;
}
.password-container {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end; /* 移至父容器 */
}
</style>