<template>

	<view v-if="visible" class="modal-overlay" @click="handleOverlayClick">
					<view class="status_bar">
			<!-- 这里是状态栏 -->
		</view>
		<view class="modal-container" @click.stop>
			<!-- 顶部标题栏 -->
			<view class="modal-header">
				<text class="modal-title">筛选清单</text>
				<view class="header-actions">
					<text class="reset-btn" @click="handleReset">重置</text>
					<text class="close-btn" @click="handleClose">×</text>
				</view>
			</view>

			<!-- 主体内容区域 -->
			<view class="modal-content">
				<!-- 左侧工序分类区域 -->
				<view class="left-panel">
					<view class="panel-title">
						<text class="title-text">工序分类</text>
					</view>
					<scroll-view class="category-scroll" scroll-y="true" show-scrollbar="false">
						<view class="category-list">
							<view 
								v-for="category in stepCategories" 
								:key="category.stepCateId"
								class="category-item"
								:class="{ 'active': selectedStepCateId === category.stepCateId }"
								@click="handleStepCategorySelect(category.stepCateId)"
							>
								<text class="category-name">{{ category.cateName }}</text>
							</view>
						</view>
					</scroll-view>
				</view>

				<!-- 右侧筛选条件区域 -->
				<view class="right-panel">
					<!-- 搜索框 -->
					<view class="search-section">
						<view class="search-box">
							<text class="search-icon">🔍</text>
							<input
								class="search-input"
								v-model="searchKeyword"
								placeholder="搜索清单名称"
							/>
						</view>
					</view>

					<!-- 工序列表区域 -->
					<view class="filter-section">
						<view class="section-title">
							<text class="section-title-text">工序分类</text>
						</view>
						<view class="tag-container">
							<view v-if="stepList.length === 0" class="empty-tip">
								<text class="empty-text">请先选择工序分类</text>
							</view>
							<view v-else class="tag-list">
								<view 
									v-for="step in stepList" 
									:key="step.stepId"
									class="tag-item"
									:class="{ 'selected': selectedStepIds.includes(step.stepId) }"
									@click="handleStepSelect(step.stepId)"
								>
									<text class="tag-text">{{ step.stepName }}</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 清单分类区域 -->
					<view class="filter-section">
						<view class="section-title">
							<text class="section-title-text">清单分类</text>
						</view>
						<view class="tag-container">
							<view class="tag-list">
								<view 
									v-for="inventoryCategory in inventoryCategories" 
									:key="inventoryCategory.id"
									class="tag-item"
									:class="{ 'selected': selectedInventoryCateIds.includes(inventoryCategory.id) }"
									@click="handleInventoryCategorySelect(inventoryCategory.id)"
								>
									<text class="tag-text">{{ inventoryCategory.name }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部确认按钮 -->
			<view class="modal-footer">
				<view class="filter-info">
					<text class="filter-count">已设置 {{ getFilterCount() }} 个筛选条件</text>
				</view>
				<view class="confirm-button" @click="handleConfirm">
					<text class="confirm-text">确认筛选</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed, watch, onMounted } from 'vue'
	import { getStepCategoryList, getStepList, getInventoryCategoryList } from '@/common/api.uts'
	import type { StepCategory, Step, InventoryCategory } from '@/utils/apiType.uts'

	// Props
	const props = defineProps({
		visible: {
			type: Boolean,
			default: false
		}
	})

	// Emits
	const emit = defineEmits(['close', 'confirm'])

	// 响应式数据
	const loading = ref<boolean>(false)
	const searchKeyword = ref<string>('')
	
	// 筛选条件
	const selectedStepCateId = ref<string>('')
	const selectedStepIds = ref<Array<string>>([])
	const selectedInventoryCateIds = ref<Array<string>>([])

	// 数据源
	const stepCategories = ref<Array<StepCategory>>([])
	const stepList = ref<Array<Step>>([])
	const inventoryCategories = ref<Array<InventoryCategory>>([])

	// TODO: 模拟数据 - 工序分类列表
	const mockStepCategories = ref<Array<StepCategory>>([
		{
			stepCateId: "1953023017944686593",
			cateName: "路基工程",
			createTime: "2025-08-06 17:19:08"
		},
		{
			stepCateId: "1951219463051325441", 
			cateName: "桥梁工程",
			createTime: "2025-08-01 17:52:27"
		},
		{
			stepCateId: "1951219463051325442",
			cateName: "隧道工程", 
			createTime: "2025-08-01 17:52:27"
		},
		{
			stepCateId: "1951219463051325443",
			cateName: "涵洞工程",
			createTime: "2025-08-01 17:52:27"
		},
		{
			stepCateId: "1951219463051325444",
			cateName: "养护工程",
			createTime: "2025-08-01 17:52:27"
		}
	])

	// TODO: 模拟数据 - 工序列表
	const mockStepData = ref<Array<Step>>([
		{
			stepId: "1951219463110045697",
			stepName: "初支",
			stepCode: "CCZZ",
			stepAlias: null,
			stepSort: 0,
			cateIds: "1953023017944686593",
			cateNames: "路基工程",
			createTime: "2025-08-01 17:52:27"
		},
		{
			stepId: "1951219463110045698",
			stepName: "二衬", 
			stepCode: "EL",
			stepAlias: null,
			stepSort: 1,
			cateIds: "1953023017944686593",
			cateNames: "路基工程",
			createTime: "2025-08-01 17:52:27"
		},
		{
			stepId: "1951219463110045699",
			stepName: "仰拱",
			stepCode: "YG", 
			stepAlias: null,
			stepSort: 2,
			cateIds: "1951219463051325441",
			cateNames: "桥梁工程",
			createTime: "2025-08-01 17:52:27"
		}
	])

	// TODO: 模拟数据 - 清单分类列表
	const mockInventoryCategories = ref<Array<InventoryCategory>>([
		{
			id: "1951219462925496322",
			name: "超前及地质不良作业线",
			createTime: "2025-08-01 17:52:27"
		},
		{
			id: "1951219462925496323",
			name: "开挖作业线",
			createTime: "2025-08-01 17:52:27"
		},
		{
			id: "1951219462925496324", 
			name: "锚喷支护作业线",
			createTime: "2025-08-01 17:52:27"
		},
		{
			id: "1951219462925496325",
			name: "仰拱支护作业线",
			createTime: "2025-08-01 17:52:27"
		},
		{
			id: "1951219462925496326",
			name: "沟槽作业线",
			createTime: "2025-08-01 17:52:27"
		},
		{
			id: "1951219462925496327",
			name: "检查作业线",
			createTime: "2025-08-01 17:52:27"
		},
		{
			id: "1951219462925496328",
			name: "文明施工",
			createTime: "2025-08-01 17:52:27"
		}
	])

	// API调用函数
	const loadStepCategories = async (): Promise<void> => {
		try {
			loading.value = true
			// TODO: 调用真实API接口
			// const response = await getStepCategoryList()
			// if (response.code === 200 && response.data != null) {
			//     stepCategories.value = response.data as Array<StepCategory>
			//     console.log('工序分类加载成功:', stepCategories.value)
			// } else {
			//     stepCategories.value = mockStepCategories.value
			//     console.log('API调用失败，使用模拟数据:', stepCategories.value)
			// }

			// 使用模拟数据
			stepCategories.value = mockStepCategories.value
			console.log('工序分类加载成功(模拟数据):', stepCategories.value)
		} catch (error) {
			console.error('加载工序分类失败:', error)
			// 异常时使用模拟数据
			stepCategories.value = mockStepCategories.value
			uni.showToast({
				title: '加载工序分类失败',
				icon: 'none'
			})
		} finally {
			loading.value = false
		}
	}

	const loadStepList = async (stepCateId: string): Promise<void> => {
		try {
			// TODO: 调用真实API接口
			// const response = await getStepList(stepCateId)
			// if (response.code === 200 && response.data != null) {
			//     stepList.value = response.data as Array<Step>
			//     console.log('工序列表加载成功:', stepList.value)
			// } else {
			//     stepList.value = mockStepData.value.filter(function(step): boolean {
			//         return step.cateIds === stepCateId
			//     })
			//     console.log('API调用失败，使用模拟数据:', stepList.value)
			// }

			// 使用模拟数据 - 根据工序分类ID筛选
			stepList.value = mockStepData.value.filter(function(step): boolean {
				return step.cateIds === stepCateId
			})
			console.log('工序列表加载成功(模拟数据):', stepList.value)
		} catch (error) {
			console.error('加载工序列表失败:', error)
			// 异常时使用模拟数据
			stepList.value = mockStepData.value.filter(function(step): boolean {
				return step.cateIds === stepCateId
			})
			uni.showToast({
				title: '加载工序列表失败',
				icon: 'none'
			})
		}
	}

	const loadInventoryCategories = async (): Promise<void> => {
		try {
			// TODO: 调用真实API接口
			// const response = await getInventoryCategoryList()
			// if (response.code === 200 && response.data != null) {
			//     inventoryCategories.value = response.data as Array<InventoryCategory>
			//     console.log('清单分类加载成功:', inventoryCategories.value)
			// } else {
			//     inventoryCategories.value = mockInventoryCategories.value
			//     console.log('API调用失败，使用模拟数据:', inventoryCategories.value)
			// }

			// 使用模拟数据
			inventoryCategories.value = mockInventoryCategories.value
			console.log('清单分类加载成功(模拟数据):', inventoryCategories.value)
		} catch (error) {
			console.error('加载清单分类失败:', error)
			// 异常时使用模拟数据
			inventoryCategories.value = mockInventoryCategories.value
			uni.showToast({
				title: '加载清单分类失败',
				icon: 'none'
			})
		}
	}

	// 计算筛选条件数量
	const getFilterCount = (): number => {
		let count = 0
		if (selectedStepCateId.value !== '') count++
		if (selectedStepIds.value.length > 0) count += selectedStepIds.value.length
		if (selectedInventoryCateIds.value.length > 0) count += selectedInventoryCateIds.value.length
		if (searchKeyword.value.trim() !== '') count++
		return count
	}

	// 事件处理函数
	const handleClose = (): void => {
		emit('close')
	}

	const handleOverlayClick = (): void => {
		handleClose()
	}

	const handleReset = (): void => {
		selectedStepCateId.value = ''
		selectedStepIds.value = []
		selectedInventoryCateIds.value = []
		searchKeyword.value = ''
		stepList.value = []
		console.log('筛选条件已重置')
	}

	const handleStepCategorySelect = (stepCateId: string): void => {
		selectedStepCateId.value = stepCateId
		selectedStepIds.value = [] // 清空之前选择的工序
		// 异步加载工序列表
		loadStepList(stepCateId).then(function() {
			console.log('工序列表加载完成')
		}).catch(function(error) {
			console.error('工序列表加载失败:', error)
		})
		console.log('选择工序分类:', stepCateId)
	}

	const handleStepSelect = (stepId: string): void => {
		const index = selectedStepIds.value.indexOf(stepId)
		if (index > -1) {
			selectedStepIds.value.splice(index, 1)
		} else {
			selectedStepIds.value.push(stepId)
		}
		console.log('工序选择状态更新:', selectedStepIds.value)
	}

	const handleInventoryCategorySelect = (cateId: string): void => {
		const index = selectedInventoryCateIds.value.indexOf(cateId)
		if (index > -1) {
			selectedInventoryCateIds.value.splice(index, 1)
		} else {
			selectedInventoryCateIds.value.push(cateId)
		}
		console.log('清单分类选择状态更新:', selectedInventoryCateIds.value)
	}



	const handleConfirm = (): void => {
		// 构建筛选条件数据
		const filterData = {
			stepCateId: selectedStepCateId.value,
			stepIds: selectedStepIds.value,
			inventoryCateIds: selectedInventoryCateIds.value,
			searchKeyword: searchKeyword.value.trim()
		}

		console.log('确认筛选条件:', filterData)
		emit('confirm', filterData)
	}

	// 监听弹窗显示状态
	watch(function(): boolean {
		return props.visible
	}, function(newVisible: boolean) {
		if (newVisible) {
			// 弹窗打开时初始化数据
			loadStepCategories()
			loadInventoryCategories()
			
			// 恢复之前的筛选条件
			try {
				const savedFilter = uni.getStorageSync('checklistFilterData')
				if (savedFilter != null && savedFilter !== '') {
					const filterData = JSON.parse(savedFilter as string) as UTSJSONObject
					const stepCateId = filterData['stepCateId'] as string
					const stepIds = filterData['stepIds'] as Array<string>
					const inventoryCateIds = filterData['inventoryCateIds'] as Array<string>
					const searchKeywordValue = filterData['searchKeyword'] as string
					
					selectedStepCateId.value = stepCateId != null ? stepCateId : ''
					selectedStepIds.value = stepIds != null ? stepIds : []
					selectedInventoryCateIds.value = inventoryCateIds != null ? inventoryCateIds : []
					searchKeyword.value = searchKeywordValue != null ? searchKeywordValue : ''
					
					// 如果有选中的工序分类，加载对应的工序列表
					if (selectedStepCateId.value !== '') {
						loadStepList(selectedStepCateId.value).then(function() {
							console.log('恢复工序列表加载完成')
						}).catch(function(error) {
							console.error('恢复工序列表加载失败:', error)
						})
					}
					console.log('恢复筛选条件:', filterData)
				}
			} catch (error) {
				console.error('恢复筛选条件失败:', error)
			}
		}
	})
</script>

<style scoped>
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(91, 91, 92,0.7);
		z-index: 9999;
		flex-direction: column;
	}

	.modal-container {
		width: 100%;
		height: 85%;
		background-color: #FFFFFF;
		flex-direction: column;
		overflow: hidden;
	}

	/* 顶部标题栏 */
	.modal-header {
		background-color: #FFFFFF;
		height: 88rpx;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.modal-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}

	.header-actions {
		flex-direction: row;
		align-items: center;
	}

	.reset-btn {
		font-size: 28rpx;
		color: #1976D2;
		margin-right: 30rpx;
	}

	.close-btn {
		font-size: 40rpx;
		color: #999999;
		width: 40rpx;
		height: 40rpx;
		text-align: center;
		line-height: 40rpx;
	}

	/* 主体内容区域 */
	.modal-content {
		flex: 1;
		flex-direction: row;
		background-color: #FFFFFF;
	}

	/* 左侧工序分类面板 */
	.left-panel {
		width: 200rpx;
		background-color: #f5f5f5;
		border-right: 1rpx solid #e0e0e0;
		flex-direction: column;
	}

	.panel-title {
		height: 80rpx;
		align-items: center;
		justify-content: center;
		background-color: #FFFFFF;
		border-bottom: 1rpx solid #e9ecef;
	}

	.title-text {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
	}

	.category-scroll {
		flex: 1;
	}

	.category-list {
		flex-direction: column;
	}

	.category-item {
		height: 80rpx;
		align-items: center;
		justify-content: center;
		padding: 0 15rpx;
		border-bottom: 1rpx solid #e0e0e0;
	}

	.category-item.active {
		background-color: #007AFF;
		border-right: none;
	}

	.category-name {
		font-size: 24rpx;
		color: #333333;
		text-align: center;
		line-height: 1.2;
	}

	.category-item.active .category-name {
		color: #FFFFFF;
		font-weight: bold;
	}

	/* 右侧筛选条件面板 */
	.right-panel {
		flex: 1;
		flex-direction: column;
		padding: 30rpx 20rpx;
		background-color: #FFFFFF;
	}

	/* 搜索区域 */
	.search-section {
		margin-bottom: 30rpx;
		padding: 0 20rpx;
	}

	.search-box {
		background-color: #f5f5f5;
		border-radius: 8rpx;
		height: 80rpx;
		flex-direction: row;
		align-items: center;
		padding: 0 20rpx;
		border: 1rpx solid #e0e0e0;
	}

	.search-icon {
		font-size: 28rpx;
		color: #999999;
		margin-right: 20rpx;
	}

	.search-input {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}

	/* 筛选区域 */
	.filter-section {
		margin-bottom: 50rpx;
		padding: 0 20rpx;
	}

	.section-title {
		margin-bottom: 30rpx;
	}

	.section-title-text {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}

	.tag-container {
		min-height: 100rpx;
	}

	.empty-tip {
		height: 100rpx;
		align-items: center;
		justify-content: center;
	}

	.empty-text {
		font-size: 26rpx;
		color: #999999;
	}

	.tag-list {
		flex-direction: row;
		flex-wrap: wrap;
	}

	.tag-item {
		background-color: #f0f0f0;
		border: 1rpx solid #d0d0d0;
		border-radius: 8rpx;
		padding: 20rpx 30rpx;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		margin-right: 20rpx;
		min-width: 120rpx;
	}

	.tag-item.selected {
		background-color: #007AFF;
		border-color: #007AFF;
	}

	.tag-text {
		font-size: 28rpx;
		color: #333333;
		text-align: center;
	}

	.tag-item.selected .tag-text {
		color: #FFFFFF;
		font-weight: bold;
	}

	/* 底部操作区域 */
	.modal-footer {
		background-color: #FFFFFF;
		border-top: 1rpx solid #e0e0e0;
		padding: 30rpx;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}

	.filter-info {
		flex: 1;
	}

	.filter-count {
		font-size: 28rpx;
		color: #666666;
	}

	.confirm-button {
		background-color: #007AFF;
		border-radius: 8rpx;
		padding: 28rpx 60rpx;
		align-items: center;
		justify-content: center;
		min-width: 240rpx;
	}

	.confirm-text {
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: bold;
	}
</style>
