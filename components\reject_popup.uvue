<template>
	<view class="mask_picker"  v-if="show">
		<view class="picker_content">
			<view class="reject_content">
				<text class="reject_title">{{title}}：</text>
				<textarea v-model="remark" class="reject_textarea" placeholder="" ></textarea>
			</view>
			<view class="picker_bottom">
				<button class="quxiao reject_btn" @click="handlePickerClose">
					取消
				</button>
				<button class="queren reject_btn" @click="handlePickerConfirm">
					确认
				</button>
			</view>
		</view>
	</view>
</template>

<script setup>
	
	const props = defineProps({
		show:{
			type: Boolean,
			default: false
		},
		title:{
			type:String,
			default:''
		}
	})
	
	const emit=defineEmits(['close','confirm'])
	
	const remark=ref<string>('')
	
	// const show=ref<boolean>(false)
	// 关闭
	const handlePickerClose=()=>{
		emit('close')
	}
	const handlePickerConfirm=()=>{
		// console.log('remark',remark.value);
		emit('confirm',remark.value)
	}
</script>

<style lang="scss" scoped>
.mask_picker{
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 10000;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
}
.picker_content{
	width:90%;
	margin:0 auto;
	background: #fff;
	border-radius: 10rpx;
	// position: absolute;
	// left: 50%;
	// top: 50%;
	.reject_content{
		padding: 20rpx 30rpx;
		.reject_title{
			padding-top: 30rpx;
			font-size: 30rpx;
			color:#131313;
		}
		.reject_textarea{
			width: 100%;
			margin: 0 auto;
			margin-top: 20rpx;
			padding: 10rpx 20rpx;
			background: #F3F3F3;
			font-size: 26rpx;
		}
	}
}
.picker_bottom{
	margin: 0 auto;
	width: 100%;
	padding: 20rpx 40rpx;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	
	.reject_btn{
		width: 45%;
		font-size: 30rpx;
	}
	.bohui{
		background: #EFEFEF;
		color: #131313;
	}
	.queren{
		background: #0189F6;
		color: #fff;
	}
	
}
.quxiao{
	color: #888;
}
.queren{
	color: #007aff;
}
</style>