<template>
	<view class="status_bar">
		
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title_header">员工调整</text>
	
	    <view class="nav-btn"  ></view>
	</view>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="page-container">
			<view class="info_header">
				<text class="iconfont tz_icon">&#xe63e;</text>
				<text class="title">调整信息填写</text>
			</view>
			<view class="form_div">
				<text class="form_label">选择员工：</text>
				<view class="title_content" @click="handelSelect(1)">
					<text class="xz_title">{{fromInfo.name}}</text>
					<text class="iconfont xiala_icon">&#xe747;</text>
				</view>
			</view>
			<view class="form_div">
				<text class="form_label">员工当前状态：</text>
				<text class="form_label"></text>
			</view>
			<view class="form_div">
				<text class="form_label">员工调整状态：</text>
				<view class="title_content" @click="handelSelect(2)">
					<text class="xz_title">{{fromInfo.statusName}}</text>
					<text class="iconfont xiala_icon">&#xe747;</text>
				</view>
			</view>
			<view class="form_div" style="border: none;">
				<text class="form_label">调整原因：</text>
				
			</view>
			<textarea v-model="fromInfo.remark" class="tz_remark_textarea" placeholder="请填写调整原因" ></textarea>
			
			
			<view class="info_header margin_top20">
				<text class="iconfont sp_icon">&#xe665;</text>
				<text class="title">审批流程</text>
			</view>
			<steps :nodeOption="entryDetailsList" type="2"></steps>
			
			
			<button @click="handleSubmit" class="submit_btn">提交</button>
		</view>
		
		<selectPicker v-if="showPicker"  :show="showPicker" @close="pickerColse" @confirm="pickerVonfirm" :items="items" :value="people_id"></selectPicker>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import {ProjectOption,confirmHcType,ygtzType,entryDeatilsType} from '@/utils/apiType.uts'
	import selectPicker from '@/components/select_picker_gj.uvue'
	import steps from '@/components/steps.uvue'
	
	const taskId=ref<string>('')
	
	const showPicker=ref<boolean>(false)
	
	const fromInfo=ref<ygtzType>({
		name:'',
		nameId:'',
		status:'',
		statusName:'',
		remark:'',
	})
	
	// 选中的id
	const people_id=ref<string|number|null>('1')
	
	// 选择的数据
	const peopleList =ref<ProjectOption[]>([
		{value:'1952551639793594370',label:'赵某某'},
		{value:'1952551639793594371',label:'钱某某'},
		{value:'1952551639793594372',label:'孙某某'},
		{value:'1952551639793594373',label:'李某某'},
	])
	
	// 选择的数据
	const statusList =ref<ProjectOption[]>([
		{value:'1952551639793594370',label:'入职'},
		{value:'1952551639793594371',label:'离职'},
		{value:'1952551639793594372',label:'调岗中'},
		{value:'1952551639793594373',label:'调岗完成'},
	])
	
	// 传入组件的参数
	const items=ref<ProjectOption[]>([])
	
	// 选择的第几个下拉
	const currentIndex=ref<number|null>(null)
	
	const entryDetailsList=ref<entryDeatilsType[]>([
		{title:'施工部土建工程师',name:'李某某',time:'2025/03/02 15:20:30',status:2,rejectContent:'',id:1},
		{title:'办公室',name:'张某某',time:'2025/03/02 17:20:30',status:2,rejectContent:'',id:2},
		{title:'部门领导',name:'王某某',time:'2025/03/02 17:20:30',status:3,rejectContent:'工作未结尾，不予调整',id:3},
		{title:'部门主管',name:'王某某',time:'2025/03/02 17:20:30',status:1,rejectContent:'',id:4},
	])
	
	// 打开弹窗组件
	const handelSelect=(i:number)=>{
		currentIndex.value=i
		if(i==1){
			items.value=peopleList.value
		}else if(i==2){
			items.value=statusList.value
		}
		showPicker.value=true
	}
	
	// 关闭选择组件
	const pickerColse=()=>{
		showPicker.value=false
	}
	// 回传关闭组件
	const pickerVonfirm=(data:confirmHcType)=>{
		// console.log('data',data);
		
		if(data.selectedItem!=null){
			if(currentIndex.value==1){
				const label=data.selectedItem['label']
				if (label != null) {
				  fromInfo.value.name = label as string
				}
				const value=data.selectedItem['value']
				if (value != null) {
				  fromInfo.value.nameId = value as string
				}
			}else if(currentIndex.value==2){
				const label=data.selectedItem['label']
				if (label != null) {
				  fromInfo.value.statusName = label as string
				}
				const value=data.selectedItem['value']
				if (value != null) {
				  fromInfo.value.status = value as string
				}
			}
			
		}
		
		showPicker.value=false
	}
	
	// 提交
	const handleSubmit=()=>{
		uni.showModal({
			title: '提示',
			content: '确定提交？',
			confirmText: '确定',
			cancelText: '取消',
			success: (res) => {
				if (res.confirm) {
					console.log('用户点击确定');
					// 执行删除操作
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	
	onLoad((option: OnLoadOptions) => {
		// 安全获取属性值
		const id = option['id']
		if (id != null) {
		  taskId.value = id as string
		}
	})
</script>

<style lang="scss" scoped>
	
.page-container{
	background-color: #ffffff;
}
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 10rpx;

  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */

}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title_header {
  width: 50%;
  text-align: center;
  font-size: 36rpx;
  // font-weight: bold;
  color: #131313; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_font{
	font-size: 32rpx;
	 color: #707070; /* 黑色文字 */
}

.info_header{
	width: 100%;
	background: #F7F8FA;
	padding: 30rpx 40rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	.title_icon{
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	.title{
		color: #131313;
		font-size: 32rpx;
		font-weight: bold;
	}
	.tz_icon{
		font-size: 40rpx;
		color: #58bbf6;
		margin-right: 10rpx;
	}
}
.form_div{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #e5e5e5;
}
.form_label{
	color: #353535;
	font-size:30rpx;
}

/* 其他原有样式保持不变 */
.title_content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  // width: 400%;
  flex: 1;
}

.xz_title {
  font-size: 36rpx;
  color: #131313;
}


.xiala_icon {
  font-size: 40rpx;
  color: #707070;
  text-align: right;
}
.tz_remark_textarea{
	width: 90%;
	margin: 0 auto;
	margin-top: 20rpx;
	padding: 20rpx 20rpx;
	background: #F3F3F3;
	font-size: 26rpx;
}
.margin_top20{
	margin-top: 20rpx;
}
.sp_icon{
	font-size: 32rpx;
	color: #58bbf6;
	margin-right: 10rpx;
}
.submit_btn{
	width: 90%;
	margin: 0 auto;
	margin-top: -20rpx;
	margin-bottom: 20rpx;
	border-radius: 40rpx;
	background: #0189F6;
	box-shadow: 3rpx 7rpx 12rpx 1rpx rgba(1,137,246,0.24);
	color: #fff;
	font-size: 29rpx;
}
</style>
