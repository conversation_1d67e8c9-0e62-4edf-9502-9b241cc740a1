<template>
	<view class="status_bar">
		
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title_header">员工入职审批</text>
	
	    <view class="nav-btn"  >
	    </view>
	</view>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="page-container">
			<view class="info_header">
				<image class="title_icon" src="/static/image/grxx.png" mode=""></image>
				<text class="title">个人基本信息</text>
			</view>
			<view class="form_div">
				<text class="form_label">姓名：</text>
				<text class="form_label">{{mineInfo.nickName}}</text>
			</view>
			<view class="form_div">
				<text class="form_label">性别：</text>
				<text class="form_label" >{{mineInfo.sex=='1'?"男":"女"}}</text>
				
			</view>
			<view class="form_div">
				<text class="form_label">年龄：</text>
				<text class="form_label" >{{mineInfo.age}}</text>
				
			</view>
			<view class="form_div">
				<text class="form_label">手机号：</text>
				<text class="form_label" >{{mineInfo.phone}}</text>
				
			</view>
			<view class="form_div">
				<text class="form_label">工号：</text>
				<text class="form_label" >{{mineInfo.workNum}}</text>
				
			</view>
			<view class="form_div">
				<text class="form_label">所属项目部：</text>
				<text class="form_label">{{mineInfo.tenantName}}</text>
				
				
			</view>
			<view class="form_div">
				<text class="form_label">任职部门：</text>
				<text class="form_label" >{{mineInfo.deptNmae}}</text>
			
				
				
			</view>
			<view class="form_div">
				<text class="form_label">岗位：</text>
				<text class="form_label" >{{mineInfo.postName}}</text>
			
				
				
			</view>
			
			<view class="info_header margin_top20">
				<text class="iconfont sp_icon">&#xe665;</text>
				<text class="title">审批流程</text>
			</view>
			<steps :nodeOption="entryDetailsList" type="1"></steps>
			
			<view class="buttom_btn">
				<button @click="handlereject" class="btn bohui">驳回</button>
				<button @click="handleconfirm" class="btn queren">确认</button>
			</view>
		</view>
		<rejectPopup v-if="showReject" :show="showReject" :title="reject_title" @close="rejectClose" @confirm="rejectConfirm"></rejectPopup>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref } from 'vue'
	import {confirmHcType,ProjectOption,entryDeatilsType} from '@/utils/apiType.uts'
	import selectPicker from '@/components/select_picker.uvue'
	import treeSelect from '@/components/treeSelect.uvue'
	import steps from '@/components/steps.uvue'
	import rejectPopup from '@/components/reject_popup.uvue'
	
	// 是否可以修改 1是不可以修改 2 是可以修改
	const isEdit=ref<number>(1)
	// 弹窗标题
	const reject_title=ref<string>('驳回原因')
	// 个人信息
	const mineInfo=ref<UTSJSONObject>({
		nickName:'张无忌',
		sex:1,
		age:32,
		phone:'13466885696',
		workNum:'XMB5896111',
		tenantId:1,
		tenantName:'一二三项目部',
		deptId:2,
		deptNmae:'工程部',
		postId:1,
		postName:'施工员',
	})
	
	const entryDetailsList=ref<entryDeatilsType[]>([
		{title:'施工部土建工程师',name:'李某某',time:'2025/03/02 15:20:30',status:2,rejectContent:'',id:1},
		{title:'办公室',name:'张某某',time:'2025/03/02 17:20:30',status:2,rejectContent:'',id:2},
		{title:'部门领导',name:'王某某',time:'2025/03/02 17:20:30',status:3,rejectContent:'工作未结尾，不予调整',id:3},
		{title:'部门主管',name:'王某某',time:'2025/03/02 17:20:30',status:1,rejectContent:'',id:4},
	])
	
	
	const taskId=ref<string>('')
	// 驳回的弹窗控制
	const showReject=ref<boolean>(false)
	
	// 驳回
	const handlereject=()=>{
		console.log('驳回');
		showReject.value=true
		
		
	}
	// 确认
	const handleconfirm=()=>{
		console.log('确认');
		 uni.showModal({
			title: '提示',
			content: '确定要同意？',
			confirmText: '确定',
			cancelText: '取消',
			success: (res) => {
				if (res.confirm) {
					console.log('用户点击确定');
					// 执行删除操作
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	// 驳回取消
	const rejectClose=()=>{
		showReject.value=false
	}
	// 驳回确认
	const rejectConfirm=(data:string)=>{
		console.log('驳回确认',data);
		showReject.value=false
	}
	
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	
	onLoad((option: OnLoadOptions) => {
		// 安全获取属性值
		const id = option['id']
		if (id != null) {
		  taskId.value = id as string
		}
	})
</script>

<style lang="scss" scoped>
.page-container{
	background-color: #ffffff;
	// height: 100%;
}
.info_header{
	width: 100%;
	background: #F7F8FA;
	padding: 30rpx 40rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	.title_icon{
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	.title{
		color: #131313;
		font-size: 32rpx;
		font-weight: bold;
	}
	.sp_icon{
		font-size: 32rpx;
		color: #58bbf6;
		margin-right: 10rpx;
	}
}
.form_div{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 40rpx 40rpx;
	border-bottom: 1rpx solid #e5e5e5;
}
.form_label{
	color: #353535;
	font-size:30rpx;
}
.radio{
	margin-right: 20rpx;
}
.margin_top20{
	margin-top: 20rpx;
}
.buttom_btn{
	margin-top: 40rpx;
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	margin-bottom: 20rpx;
	.btn{
		width: 40%;
		padding: 0rpx 40rpx;
		border-radius: 50rpx;
	}
	.bohui{
		background: #EFEFEF;
		color: #131313;
	}
	.queren{
		background: #0189F6;
		color: #fff;
	}
}
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 10rpx;

  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */

}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title_header {
  width: 50%;
  text-align: center;
  font-size: 36rpx;
  // font-weight: bold;
  color: #131313; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_font{
	font-size: 32rpx;
	 color: #707070; /* 黑色文字 */
}
</style>
