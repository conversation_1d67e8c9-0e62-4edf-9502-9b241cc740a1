<template>
	<view class="status_bar">
		<!-- 这里是状态栏 -->
	</view>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<image
		   class="background-image" 
		   src="/static/image/mine_bj.png"
		   mode="cover"
		   
		 />
		
		<view class="page-container" >
		 <view class="mine_header">
		 	<text class="mine_title">个人中心</text>
		 </view>
		  <view class="approval-section" v-if="isEntry==1">
			<view class="text-container" v-if="accountStatus==3">
			  <text class="status-text" >已驳回</text>
			  <text class="status-desc">
				请重新填写入职信息
			  </text>
			</view>
			<text class="approval-status" v-else-if="accountStatus==1">审批中</text>
			<text class="approval-status" v-else-if="accountStatus==2">岗位分配中</text>
			<text class="detail-btn" @click="checkentryDetails">查看入职详情</text>
			
		  </view>
		  <view class="entry" v-else-if="isEntry==2">
		  	<view class="entry_left">
		  		<image class="avatar" src="/static/image/mrAvatar.png" mode="widthFix"></image>
		  	</view>
			<view class="entry_right">
				<view class="entry_name">
					<text class="font_color_bai font_size">张某某</text>
					<view class="tubiao">
						<text class="iconfont icon_fonts nan">&#xe8b3;</text>
						<!-- <text class="iconfont icon_fonts nv">&#xe8b4;</text> -->
					</view>
				</view>
				<view class="mine_two">
					
					<view class="zhiwei">
						
						<text class="font_color_bai font_size_28">工程部长</text>
					</view>
					<view class="zaizhi">
						
						<text class="font_color_lv font_size_28">在职</text>
					</view>
					
				</view>
				<view class="ssxmb">
					<text class="font_color_bai font_size_28">所属项目部：</text>
					<text class="font_color_bai font_size_28" style="flex:1">一二三项目部</text>
				</view>
			</view>
		  </view>
		  <view class="menu-list">
			  <view 
				class="menu-item" 
				v-for="(item, index) in filteredMenuItems" 
				:key="index"
				@click="handleJump(item)"
			  >
				<text class="item-text">{{ item.label }}</text>
				<text class="iconfont icon_font" v-if="filteredMenuItems.length-1!=index">&#xe747;</text>
			  </view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, computed } from 'vue'
	import {menuItemsType} from '@/utils/apiType.uts'
	
	
	// 入职还是已经入职 1未入职  2已入职
	const isEntry=ref<string|number>(2)
	// 当前员工的入职状态 
	const accountStatus=ref<string|number|null>(3)
	// 当前是员工还是主管
	const identity=ref<string|number>(2)
	

	// 查看入职详情
	const checkentryDetails=()=>{
		console.log('查看入职详情');
		uni.navigateTo({
			url:'/pages/mine/entryDetails'
		})
	}
	
	const menuItems =ref<menuItemsType[]>([
		{ label:'个人信息',url:'/pages/mine/mineInfo',permission:1,id:1},
		{ label:'我的证书',url:'/pages/mine/zhengshu/mineZhengshu',permission:1,id:2},
		{ label:'转办记录',url:'/pages/mine/zhuanban/transferRecord',permission:1,id:3},
		{ label:'修改密码',url:'/pages/login/editPassword',permission:1,id:4},
		{ label:'评分记录',url:'/pages/mine/pingfenjilu/weekRate',permission:2,id:5},
		{ label:'员工调整记录',url:'/pages/mine/tiaozhengjilu/staffTzjl',permission:2,id:6},
		{ label:'退出登录',url:'',permission:1,id:7},
	])
	
	// 计算属性：根据权限过滤菜单项
	const filteredMenuItems = computed(function(): menuItemsType[] {
	  if (identity.value == 2) {
	    // 权限为2时显示全部菜单项
	    return menuItems.value
	  } else {
	    // 权限为1时只显示permission为1的项
	    return menuItems.value.filter(function(item): boolean {
	      return item.permission == 1
	    })
	  }
	})
	
	// 页面跳转和处理事件
	const handleJump=(item:menuItemsType)=>{
		// console.log('页面跳转',item);
		if (item.url != null && item.url != '') {
			uni.navigateTo({
				url:item.url as string
			})
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
  // background-color: #ffffff;
  height: 100%;
  
}
.background-image {
   width: 100%;
   height: 100%;
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   z-index: -1;
}
.mine_header{
	width: 100%;
	// height: 88px;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	margin-top: 30rpx;

}
.mine_title{
	font-size: 34rpx;
	font-weight: 400;
	color: #fff;
}

.approval-section {
  // background-color: #fff;
  width: 90%;
  height: 254rpx;
  background: #fff;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  padding: 0 80rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  border-radius: 20rpx;
  
  .approval-status {
	  font-size: 26rpx;
      color: #09bb07; /* 绿色状态文字 */
    }
  
  .detail-btn {
    background-color: #ffffff;
    color: #333333;
    font-size: 24rpx;
    padding: 8rpx 15rpx;
    border-radius: 18rpx;
	border: 1px solid #707070;
    box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  }
}
.text-container {
	display: flex;
	flex-direction: column;
	margin-top: 20rpx;
	font-size: 24rpx;
	.status-text{
		line-height: 2;
		font-size: 26rpx;
		color: #D10000;
		margin-left: 20rpx;
		
	}
	.status-desc{
		line-height: 2;
		font-size: 26rpx;
		color: #D10000;
	}
}
.menu-list {
	margin-top: 20rpx;
	padding: 0 30rpx;
	width: 90%;
	height: 100%;
	margin-left: 5%;
	background: #fff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
}
.menu-item{
	width: 100%;
	padding: 30rpx 0rpx;
	border-bottom: 1rpx solid #e5e5e5;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}
.item-text{
	font-size: 32rpx;
	color: #131313;
}
.icon_font{
	font-size: 40rpx;
	color:#707070;
} 
.entry{
	width: 90%;
	height: 254rpx;
	margin: 0 auto;
	padding: 0 20rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}
.entry_left{
	width: 120rpx;
	height: 120rpx;
	margin-right: 20rpx;
}
.entry_right{
	width: 100%;
	flex: 1;
}
.entry_name{
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 10rpx;
}
.avatar{
	width: 100%;
	
}
.tubiao{
	width: 30rpx;
	height: 30rpx;
	border-radius: 30rpx;
	background-color: #FFFFFF;
	margin-left: 10rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	
}

.icon_fonts{
	font-size: 24rpx;
	// color: #458fff;
	// #ifdef H5
	margin-top: 5rpx;
	// #endif
}
.nan{
	color: #458fff;
}
.nv{
	color: #ff4d94;
}
.font_color_bai{
	color: #fff;
}
.font_size{
	font-size: 30rpx;
}
.mine_two{
	width: 100%;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	// line-height: 1;
	margin-bottom: 10rpx;
}
.zhiwei{
	padding: 4rpx 10rpx;
	// color: #fff;
	background: rgba(204,230,255,0.35);
	border-radius: 7rpx 7rpx 7rpx 7rpx;
	border: 1rpx solid #A5D3FF;
	// font-size: 24rpx;
	// line-height: 1;
	margin-right: 10rpx;
}
.zaizhi{
	padding: 4rpx 10rpx;
	// color: #00A130;
	background: #fff;
	border-radius: 7rpx 7rpx 7rpx 7rpx;
	border: 1rpx solid #A5D3FF;
	// font-size: 24rpx;
	// line-height: 1;
	
}
.font_color_lv{
	color: #00A130;
}
.ssxmb{
	width: 100%;
	display: flex;
	flex-direction: row;
	
}
.font_size_28{
	font-size: 28rpx;
}
</style>
