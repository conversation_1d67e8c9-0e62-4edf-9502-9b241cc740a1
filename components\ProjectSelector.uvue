<template>
  <!-- 半透明遮罩层 -->
  <view class="mask" v-if="show" @click="closeModal"></view>
  
  
  <view v-if="show" class="modal-container">
    
    <view class="modal-title">选择项目部</view>

     <view class="picker-box" @click="hanldeshow">
        <text v-if="loading">加载中...</text>
        <text v-else-if="error">{{ error }}</text>
        <text v-else-if="selectActive!=null">{{ selectActive.label  }}</text>
        <text v-else-if="items.length === 0">暂无项目部数据</text>
		<text class="weiSelect" v-else> 请选择项目部</text>
      </view>

    
    
    <text class="selected-hint">当前所选：{{ selectActive==null? '未选择':selectActive.label }}</text>
    
    
    <view class="button-group">
      <button class="btn cancel" @click="closeModal">取消</button>
      <button :class="['btn', 'confirm', { 'disabled': loading || items.length === 0 || selectActive === null }]" :disabled="loading || items.length === 0 || selectActive === null" @click="handleConfirm">确认</button>
    </view>
  </view>
  
	<selectPicker v-if="showPicker"  :show="showPicker" @close="pickerColse" @confirm="pickerOpen" :items="items" :option="selectedIndex"></selectPicker>
	
</template>

<script setup>
import { ref, onMounted } from 'vue';
import {ProjectOption,confirmHcType} from '@/utils/apiType.uts'
import selectPicker from './select_picker.uvue'
import { getEmployeeProjects } from '@/common/api.uts'
// type ProjectOption = { label: string, value: string|number }

const props = defineProps({
  show: { // 控制弹窗显示
    type: Boolean,
    default: false
  }
});
type etype={
	detail:detailsType
}
type detailsType={
	value:number,
	// laber:string
}


const showPicker=ref(false)      // 控制选择器显示
const items=ref<ProjectOption[]>([])               // 可选项数据
const pickerValue=ref<number[]>([0])      // 当前选中的索引值（数组形式）
const selectedItem=ref({})     // 当前已选择的项
      // 指示器样式（上下横线）
const indicatorStyle=ref( `height: 50px; border-top: 1px solid #eee; border-bottom: 1px solid #eee;`)
const loading = ref(false)     // 加载状态
const error = ref('')          // 错误信息

const emit = defineEmits(['close', 'confirm']);


// const selectedProject = ref<string|number>(''); // 默认值
const selectedIndex=ref<number[]>([0])

const selectActive=ref<ProjectOption|null>(null)


// 关闭弹窗
const closeModal = () => {
  emit('close');
};

// 获取项目部数据
const fetchProjects = async () => {
	try {
		loading.value = true
		error.value = ''

		// const response = await getEmployeeProjects()

		/**if (response.code === 200) {
			// 直接访问 response.data
			const responseData = response.data

			if (responseData != null) {
				// 将 any 类型的数据转换为 UTSJSONObject 数组
				const dataArray = responseData as Array<UTSJSONObject>

				if (dataArray.length > 0) {
					// 将接口返回的数据转换为组件需要的格式
					const projectList: ProjectOption[] = []

					for (let i = 0; i < dataArray.length; i++) {
						const item = dataArray[i]
						if (item != null) {
							const tenantId = item.getString('tenantId')
							const projectName = item.getString('projectName')

							projectList.push({
								value: tenantId != null ? tenantId : '',
								label: projectName != null ? projectName : ''
							})
						}
					}
					items.value = projectList
				} else {
					error.value = '暂无项目部数据'
				}
			} else {
				error.value = '项目部数据格式错误'
			}
		} else {
			const msgValue = response.msg
			error.value = typeof msgValue === 'string' ? msgValue : '获取项目部数据失败'
			uni.showToast({
				title: error.value,
				icon: 'none',
				duration: 2000
			})
		} **/
	} catch (err) {
		error.value = '网络请求失败'
		uni.showToast({
			title: error.value,
			icon: 'none',
			duration: 2000
		})
		console.error('获取项目部数据失败:', err)
	} finally {
		loading.value = false
	}
}

// 确认选择
const handleConfirm = () => {
  emit('confirm', selectActive.value);
  closeModal();
};


//打开弹窗选择
const hanldeshow=()=>{
	// 每次打开时刷新数据
	if (items.value.length === 0) {
		fetchProjects()
	}

	if (loading.value) {
		uni.showToast({
			title: '数据加载中，请稍候',
			icon: 'none',
			duration: 1500
		})
		return
	}

	if (items.value.length === 0) {
		uni.showToast({
			title: '暂无项目部数据',
			icon: 'none',
			duration: 1500
		})
		return
	}

	console.log('打开弹窗');
	showPicker.value=true
}

// 打开选择组件
const pickerColse=()=>{
	showPicker.value=false
}
// 选择关闭组件
const pickerOpen=(data:confirmHcType)=>{

	selectActive.value=data.selectedItem
	// selectedProject.value=data.label
	showPicker.value=false

}

// 组件挂载时获取数据
onMounted(() => {
	fetchProjects()
})
</script>

<style scoped>

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}


.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  padding: 32rpx;
  z-index: 999;
}
.picker_popup{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	width: 100%;
	height: 100%;
}
.picker{
	position: fixed;
	width: 100%;
	height: 30%;
	background: #fff;
	bottom: 0;
	z-index: 1001;
	
}

.modal-title {
  
  text-align: center;
  display: flex;
  justify-content: center;
  font-size: 34rpx;
  font-weight: 700;
  color: #000;
  margin-bottom: 40rpx;
}


.picker-box {
  display: flex;
  justify-content: space-between;
  
  padding: 24rpx 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
  margin-bottom: 16rpx;
}


.selected-hint {
  
  color: #4CAF50;
  font-size: 26rpx;
  margin: 16rpx 0 32rpx;
  padding-left: 12rpx;
}


.button-group {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-radius: 20rpx;
}


.btn {
  flex: 1;
  border-radius: 8rpx;
  font-size: 32rpx;
  height: 80rpx;
  line-height: 80rpx;
  margin: 0 16rpx;
}


.cancel {
  background-color: #eeeeee;
  color: #666;
}


.confirm {
  background-color: #2196F3;
  color: white;
}

.disabled {
  background-color: #cccccc !important;
  color: #999999 !important;
}

.weiSelect{
	color: #666;
}


</style>