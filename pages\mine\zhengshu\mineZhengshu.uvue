<template>
	<view class="status_bar">
		<!-- 这里是状态栏 -->
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title">我的证书</text>
	
	    <view class="nav-btn" @tap="handleAdd">
			<image class="xinzneg" src="/static/image/xinz.png" mode=""></image>
	    </view>
	  </view>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="">
			
			<view class="zs_content" @click="handleDetails(item)" v-for="(item,index) in zsList" :key="index">
				<image class="item" src="/static/image/zs.png" mode=""></image>
				<view class="item_content">
					<view class="item_content_left">
						<image class="list_item_img" src="/static/image/list_item.png" mode=""></image>
						<text class="list_item_title">{{item.title}}</text>
					</view>
					<text class="iconfont icon_font">&#xe747;</text>
					
				</view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import {  zsOption } from '@/utils/apiType.uts'
	const zsList=ref<zsOption[]>([
		{id:1,title:'职业资格证书'},
		{id:2,title:'技术评定证书'},
		{id:3,title:'建筑设计师证书'},
	])
	
	const handleBack=()=> {
      uni.navigateBack()
    }
	
	// 新增证书
	const handleAdd=()=>{
		console.log('新增证书');
		uni.navigateTo({
			url:'/pages/mine/zhengshu/zhengshuDetails?type=1'
		})
	}
	// 详情
	const handleDetails=(item:zsOption)=>{
		uni.navigateTo({
			url:'/pages/mine/zhengshu/zhengshuDetails?title='+item.title+'&type=2'
		})
	}
	
</script>

<style lang="scss" scoped>
.zs_content{
	width: 95%;
	padding: 40rpx;
	margin: 0 auto;
	position: relative;
	border-radius: 10rpx;
	margin-top: 20rpx;
	.item{
		position: absolute;
		top: 0;
		height: 0;
		width: 100%;
		height: 100%;
	}
	.item_content{
		width: 100%;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		
	}
	.item_content_left{
		width: 95%;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
	}
	.list_item_img{
		width: 70rpx;
		height: 70rpx;
	}
	.list_item_title{
		font-size: 34rpx;
		margin-left: 10rpx;
		font-weight: bold;
		
	}
	.icon_font{
		font-size: 44rpx;
		color: #89949d;
	}
	
}

.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  // margin-top: 30rpx;
  padding: 30rpx 0;
  // height: 88px;
  // margin-top: 88rpx;
  // padding: 0 32rpx;
  background-color: #FFFFFF; 
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */
}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title {
  font-size: 36rpx;
  color: #000000; /* 黑色文字 */
}

/* 右侧按钮文字 */
.btn-text {
  font-size: 32rpx;
  color: #000000; /* 黑色文字 */
}
</style>
