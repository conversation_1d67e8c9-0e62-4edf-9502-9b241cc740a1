import { code401, getStorage, saveStorage } from "./getToken.uts";




// 1. 定义类型接口
type EnvConfig = {
  imgHost: string;
  urlHost: string;
  imgHosts: string;
}

// 2. 初始化配置（生产环境默认值）
const baseConfig: EnvConfig = {
  imgHost: "",
  urlHost: "",
  imgHosts: ""
};

// 3. 动态配置开发环境
const devConfig: EnvConfig = {
  imgHost: "http://*************:8080", // 开发环境实际地址
  urlHost: "http://*************:8080",
  imgHosts: "http://*************:8080"
};


// 4. 根据环境导出
export let { imgHost, urlHost, imgHosts } = 
  process.env.NODE_ENV === "development" ? devConfig : baseConfig;
  
  
  // 环境IP保存逻辑
  const storedNetwork = getStorage("network");
  
  // 严格检查逻辑
  if (
    storedNetwork == false || 
    
    // 2. 检查是否为字符串且不匹配
    (typeof storedNetwork === "string" && storedNetwork !== urlHost) ||
    
    // 3. 处理意外类型（如果是布尔值 true，需要覆盖）
    typeof storedNetwork === "boolean"
  ) {
    saveStorage({ key: "network", value: urlHost });
  }
  
// // 环境IP保存逻辑
// if(!getStorage("network") || getStorage("network") != urlHost){
//   saveStorage({key:"network",value: urlHost})
// }

// === 兼容性类型定义开始 ===
interface ServiceParams {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any; // 使用any代替复杂类型
  token?: string;
  header?: any; // 改为基本Object类型
}

interface UniResponse {
  data: ApiResponse;
  statusCode: number;
  header: any;
  errMsg?: string;
}

interface ApiResponse {
  code: number;
  msg: string;
  data: any;
}

type RequestControl = (task: any) => void;

// 请求配置（不使用泛型）
interface RequestOptionss {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any | boolean,
  token?:string,
  header?: UTSJSONObject
  timeout?: number
  showLoading?: boolean
  loadingText?: string
  isJson?: boolean
}

type Requests = {
	code: number;
	msg: string ;
	data: any|null;
};

// === 兼容性类型定义结束 ===

const service = (params: RequestOptions<RequestOptionss>) => {
	  // 请求拦截器
	  let isLoading:Boolean = false;
	  const loadingTimer = setTimeout(() => {
		if (isLoading) {
			uni.showLoading({ title: "加载中", mask: true });
		  }
	  }, 300);
	return new Promise<UniResponse>((resolve, reject)=>{
	  isLoading = true;
	  const storedValue = getStorage("network");
	  const url_: string = (typeof storedValue === 'string' && storedValue !== '') ? storedValue : urlHost;
	  let url = `${url_}${params.url}`;
	
		uni.request({
			url,
			data:params.data,
			header:params.header,
			timeout: 60000,
			method: params.method ,
			success: (res) => {
				console.log(res);

					// const resData:Requests = res.data as Requests;
					let resData: UTSJSONObject|null = null
					if (typeof res.data === 'string') {
						const jsonString: string = res.data as string
						resData = JSON.parseObject(jsonString)
					} else if (res.data != null) {
						resData = res.data as UTSJSONObject
					}
					// console.log('resData',resData);
					if(resData!=null){
						// 错误处理
						if (resData.getNumber('code') == 401) code401();
						else if (resData.getNumber('code') !== 200 ) {
							const msg = resData.getString('msg')
							uni.showToast({
							   icon: "none", 
							   duration: 4000, 
							   title: msg != null && msg != '' ? msg : "请求错误" 
							 });
							 
							 
						}
						resolve(res as UniResponse);
					}
					
				
				
			},
			fail: (err) => {
				if(err.errMsg !== "request:fail abort"){
					uni.showToast({
					   title: '',
					   icon: "none",
					   duration: 2000
					 });
					 
					 reject(err);
				}
				
			},
			 complete() {
				clearTimeout(loadingTimer); 
				isLoading = false;
				uni.hideLoading();
				uni.stopPullDownRefresh();
				
			 }
		  })
	  })
}

function createRequest(method?: 'GET' | 'POST' | 'PUT' | 'DELETE', isUpload = false) {
	return function(url: string , data?: any){
		const headers: UTSJSONObject = {
		  "Authorization": getStorage("token")==null?'':getStorage("token")
		};
		if (method !== 'GET' && !isUpload) {
			// headers={
			// 	'Content-Type':'application/json;charset=UTF-8'
			// }
			headers.set('Content-Type', 'application/json;charset=UTF-8')
		  // headers["Content-Type"] = "application/json;charset=UTF-8";
		}
		if (isUpload) {
		  headers["Content-Type"] = "multipart/form-data";
		}
		return new Promise<ApiResponse>((resolve, reject) => {
		  service(
			{ url, method, data, header: headers } as RequestOptions<RequestOptionss>
			
		  ).then(
			(res) => {
			  const resData = res.data;
			  (resData.code == 200 || resData.code == 0) ? resolve(resData) : reject(resData);
			},
			(err:any) => reject(err)
		  );
		});
	  }
}

// 方法导出
export const Get = createRequest('GET');
export const Post = createRequest('POST');
export const PostUpload = createRequest('POST', true);
export const Put = createRequest('PUT');
export const Delete = (url?: string, id?: string | number) => createRequest('DELETE')(`${url}/${id}`);


// ID参数GET请求
export function Gets(url: string, id: string | number, ) {
  return Get(`${url}/${id}`, null);
}




// 错误信息本地化
function errTip_to_zh_cn(tip: string): any {
  // 使用简单的条件判断代替对象字面量
  if (tip === "request:fail timeout") {
    return "连接超时";
  } else if (tip === "request:fail") {
    return "网络连接失败";
  } else if (tip === "request:fail abort") {
    return "请求已取消";
  } else {
    // 尝试提取更具体的错误信息
    const match = tip.match(/request:fail\s+(.*)/);
    return match ? match[1] : tip;
  }
}