<template>
			<view class="status_bar">
			<!-- 这里是状态栏 -->
		</view>
	<!-- #ifdef APP -->
	<scroll-view class="app-scroll" show-scrollbar="false">
	<!-- #endif -->
		<view class="checklist-select-container">
			<!-- 顶部导航栏 -->
			<view class="nav-bar">
				<view class="nav-left" @click="handleBack">
		      <text class="iconfont icon_font">&#xe696;</text>
				</view>
				<view class="nav-center">
					<text class="nav-title">选择清单</text>
				</view>
				<view class="nav-right">
					<text class="nav-link" @click="handleFilter">筛选</text>
				</view>
			</view>

			<!-- 搜索栏 -->
			<view class="search-container">
				<view class="search-box">
					<input
						class="search-input"
						v-model="searchKeyword"
						placeholder="搜索清单名称"
					/>
				</view>
			</view>


			<!-- 清单列表 -->
			<view class="checklist-container">
				<view v-if="loading" class="loading-container">
					<text class="loading-text">加载中...</text>
				</view>

				<view v-else-if="groupedData.length === 0" class="empty-container">
					<text class="empty-text">暂无清单数据</text>
				</view>

				<view v-else class="checklist-list">
					<!-- 遍历一级分类（工序分类名称） -->
					<view v-for="stepCategory in groupedData" :key="stepCategory.stepCateId" class="category-group">
						<view class="category-title">
							<view class="category-card"></view>
							<text class="category-title-text">{{ stepCategory.stepCateName }}</text>
						</view>

						<!-- 遍历二级分类（工序名称） -->
						<view v-for="(step,stepIndex) in stepCategory.steps" :key="step.stepId" class="step-main-group">
							<view class="step-main-title">
								<text class="step-main-title-text">{{ step.stepName }}</text>
							</view>

							<!-- 遍历三级分类（清单分类名称） -->
							<view v-for="inventoryCategory in step.inventoryCategories" :key="inventoryCategory.cateId" class="step-group">
								<view class="step-title">
									<text class="step-title-text">{{stepIndex + 1}}. {{ inventoryCategory.cateName }}</text>
									<view class="step-select-all" @click="handleStepSelectAll(inventoryCategory.items)">
										<view class="select-all-check" :class="{
											'checked': getCategorySelectStatus(inventoryCategory.items) === 'all',
											'partial': getCategorySelectStatus(inventoryCategory.items) === 'partial'
										}">
											<text class="check-icon" v-if="getCategorySelectStatus(inventoryCategory.items) === 'all'">✓</text>
											<text class="check-icon partial-icon" v-else-if="getCategorySelectStatus(inventoryCategory.items) === 'partial'">-</text>
										</view>
										<text class="select-all-text">全选</text>
									</view>
								</view>

								<!-- 遍历具体清单项 -->
								<view v-for="item in inventoryCategory.items" :key="item.inventoryId" class="checklist-item" @click="handleChecklistSelect(item)">
									<view class="item-check" :class="{ 'checked': isItemSelected(item) }">
										<text class="check-icon" v-if="isItemSelected(item)">✓</text>
									</view>
									<text class="item-name">{{ item.inventoryName }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->

	<!-- 筛选弹窗组件 -->
	<ChecklistFilterModal
		:visible="showFilterModal"
		@close="handleFilterModalClose"
		@confirm="handleFilterModalConfirm"
	/>

	<!-- 底部确认按钮 -->
	<view class="bottom-confirm-container">
		<view class="confirm-info">
			<text class="selected-count">已选择 {{ selectedChecklists.length }} 个清单</text>
		</view>
		<view class="confirm-button" @click="handleConfirm" :class="{ 'disabled': selectedChecklists.length === 0 }">
			<text class="confirm-text">确认选择</text>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted } from 'vue'
	import { getStepCategoryList, getInventoryList, getStepList, getInventoryCategoryList } from '@/common/api.uts'
	import type { StepCategory, InventoryItem, StepCategoryGroup, StepGroup, InventoryCategoryGroup, TempStepCategoryGroup } from '@/utils/apiType.uts'
	import ChecklistFilterModal from '@/components/ChecklistFilterModal.uvue'

	// 响应式数据
	const loading = ref<boolean>(false)
	const stepCategories = ref<StepCategory[]>([])
	const checklists = ref<InventoryItem[]>([])
	const selectedCategoryId = ref<string>('')
	const selectedChecklistIds = ref<string[]>([]) // 存储组合键，格式：stepCateId-stepId-cateId-inventoryId
	const selectedChecklists = ref<InventoryItem[]>([]) // 改为数组支持多选
	const searchKeyword = ref<string>('')

	// 筛选弹窗相关
	const showFilterModal = ref<boolean>(false)

	// 模拟数据 - 与API响应结构保持一致，按照图片中的层次结构组织
	const mockChecklistData = ref<InventoryItem[]>([
{
"inventoryId": "1952175749289066498", //清单id
"cateId": "1951219462925496322", //清单分类id
"stepId": "1951219463110045697", //工序id
"stepCateId": "1951219463051325441", //工序分类id
"cateName": "超前及地质不良作业线", //清单分类名称
"inventoryName": "地质素描", //清单名称
"stepName": "初支", //工序名称
"stepCateName": "隧道施工" //工序分类名称
},
{
"inventoryId": "1952175749419089921", //清单id
"cateId": "1951219462925496322", //清单分类id
"stepId": "1951219463110045697", //工序id
"stepCateId": "1951219463051325441", //工序分类id
"cateName": "超前及地质不良作业线", //清单分类名称
"inventoryName": "瞬变电磁法", //清单名称
"stepName": "初支", //工序名称
"stepCateName": "隧道施工" //工序分类名称
},
{
"inventoryId": "1952175749226151937", //清单id
"cateId": "1951219462925496322", //清单分类id
"stepId": "1951219463110045697", //工序id
"stepCateId": "1951219463051325441", //工序分类id
"cateName": "超前及地质不良作业线", //清单分类名称
"inventoryName": "加深炮孔", //清单名称
"stepName": "初支", //工序名称
"stepCateName": "隧道施工" //工序分类名称
},
{
"inventoryId": "1952175749226151937", //清单id
"cateId": "1951219462925496322", //清单分类id
"stepId": "1951219463110045698", //工序id
"stepCateId": "1951219463051325441", //工序分类id
"cateName": "超前及地质不良作业线", //清单分类名称
"inventoryName": "加深炮孔", //清单名称
"stepName": "二衬", //工序名称
"stepCateName": "隧道施工" //工序分类名称
},
{
"inventoryId": "1952175749289066498", //清单id
"cateId": "1951219462925496322", //清单分类id
"stepId": "1951219463110045697", //工序id
"stepCateId": "1953023017944686593", //工序分类id
"cateName": "超前及地质不良作业线", //清单分类名称
"inventoryName": "地质素描", //清单名称
"stepName": "初支", //工序名称
"stepCateName": "桥梁工程" //工序分类名称
},
{
"inventoryId": "1952175749419089921", //清单id
"cateId": "1951219462925496322", //清单分类id
"stepId": "1951219463110045697", //工序id
"stepCateId": "1953023017944686593", //工序分类id
"cateName": "超前及地质不良作业线", //清单分类名称
"inventoryName": "瞬变电磁法", //清单名称
"stepName": "初支", //工序名称
"stepCateName": "桥梁工程" //工序分类名称
},
{
"inventoryId": "1952175749226151937", //清单id
"cateId": "1951219462925496322", //清单分类id
"stepId": "1951219463110045697", //工序id
"stepCateId": "1953023017944686593", //工序分类id
"cateName": "超前及地质不良作业线", //清单分类名称
"inventoryName": "加深炮孔", //清单名称
"stepName": "初支", //工序名称
"stepCateName": "桥梁工程" //工序分类名称
}
])

	// 删除硬编码的假数据，改为动态生成

	// 预计算的分组数据 - 按照真实API数据结构分组
	const groupedData = ref<StepCategoryGroup[]>([])

	/**
	 * 生成清单项的唯一组合键
	 * 格式：stepCateId-stepId-cateId-inventoryId
	 * 用于区分不同工序分类下的相同清单项
	 */
	const generateCompositeKey = (item: InventoryItem): string => {
		return `${item.stepCateId}-${item.stepId}-${item.cateId}-${item.inventoryId}`
	}

	/**
	 * 从组合键中提取原始的 inventoryId
	 * 用于最终数据提交时恢复原始ID
	 */
	const extractInventoryId = (compositeKey: string): string => {
		const parts = compositeKey.split('-')
		return parts[parts.length - 1]
	}

	/**
	 * 初始化数据 - 统一的数据初始化入口
	 * 用于真实API集成时的数据处理
	 */
	const initializeData = (inventoryData: InventoryItem[]): void => {
		// 1. 将API数据存储到数据源
		mockChecklistData.value = inventoryData

		// 2. 生成分组数据
		generateGroupedData()

		// 3. 提取工序分类数据
		loadStepCategories()

		console.log('数据初始化完成，清单项数量:', inventoryData.length)
	}

	/**
	 * 动态生成分组数据 - 从 mockChecklistData 中读取数据并按四级层次结构分组
	 * 第一级：按 stepCateId 分组，显示 stepCateName（工序分类名称）
	 * 第二级：按 stepId 分组，显示 stepName（工序名称）
	 * 第三级：按 cateId 分组，显示 cateName（清单分类名称）
	 * 第四级：显示具体的 inventoryName（清单项名称）
	 */
	const generateGroupedData = (): void => {
		const result: StepCategoryGroup[] = []

		// 使用预定义类型，兼容 uni-app x
		const tempStepCategories: TempStepCategoryGroup[] = []

		// 遍历所有清单数据进行分组
		mockChecklistData.value.forEach(function(item: InventoryItem) {
			// 第一级：工序分类分组
			let stepCategoryGroup = tempStepCategories.find(sc => sc.stepCateId === item.stepCateId)
			if (stepCategoryGroup == null) {
				stepCategoryGroup = {
					stepCateId: item.stepCateId,
					stepCateName: item.stepCateName,
					tempSteps: []
				}
				tempStepCategories.push(stepCategoryGroup)
			}

			// 第二级：工序分组
			let stepGroup = stepCategoryGroup!.tempSteps.find(s => s.stepId === item.stepId)
			if (stepGroup == null) {
				stepGroup = {
					stepId: item.stepId,
					stepName: item.stepName,
					tempCategories: []
				}
				stepCategoryGroup!.tempSteps.push(stepGroup)
			}

			// 第三级：清单分类分组
			let categoryGroup = stepGroup!.tempCategories.find(c => c.cateId === item.cateId)
			if (categoryGroup == null) {
				categoryGroup = {
					cateId: item.cateId,
					cateName: item.cateName,
					items: []
				}
				stepGroup!.tempCategories.push(categoryGroup)
			}

			// 第四级：添加具体清单项
			categoryGroup!.items.push(item)
		})

		// 将临时结构转换为最终的 StepCategoryGroup 类型结构
		tempStepCategories.forEach(function(tempStepCategory) {
			const steps: StepGroup[] = []

			tempStepCategory.tempSteps.forEach(function(tempStep) {
				const inventoryCategories: InventoryCategoryGroup[] = []

				tempStep.tempCategories.forEach(function(tempCategory) {
					inventoryCategories.push({
						cateId: tempCategory.cateId,
						cateName: tempCategory.cateName,
						items: tempCategory.items
					})
				})

				steps.push({
					stepId: tempStep.stepId,
					stepName: tempStep.stepName,
					inventoryCategories: inventoryCategories
				})
			})

			result.push({
				stepCateId: tempStepCategory.stepCateId,
				stepCateName: tempStepCategory.stepCateName,
				steps: steps
			})
		})

		// 更新响应式数据
		groupedData.value = result
		console.log('动态分组数据生成完成:', result)
	}

	// 计算属性 - 过滤后的清单
	const filteredChecklists = computed(function(): InventoryItem[] {
		// 使用模拟数据而不是API数据
		let result = mockChecklistData.value

		// 按工序分类筛选
		if (selectedCategoryId.value !== '') {
			result = result.filter(function(item): boolean {
				return item.stepCateId === selectedCategoryId.value
			})
		}

		// 按搜索关键词筛选
		if (searchKeyword.value.trim() !== '') {
			const keyword = searchKeyword.value.trim().toLowerCase()
			result = result.filter(function(item): boolean {
				return item.inventoryName.toLowerCase().includes(keyword)
			})
		}

		return result
	})

	// 加载工序分类列表 - 从分组数据中动态提取
	const loadStepCategories = async (): Promise<void> => {
		try {
			// TODO: 替换为真实API调用
			// const response = await getStepCategoryList()
			// if (response.code === 200 && response.data != null) {
			//     stepCategories.value = response.data as StepCategory[]
			//     console.log('工序分类加载成功:', stepCategories.value)
			// }

			// 从分组数据中动态提取工序分类信息
			const uniqueStepCategories: StepCategory[] = []
			const stepCategorySet = new Set<string>()

			mockChecklistData.value.forEach(function(item: InventoryItem) {
				if (stepCategorySet.has(item.stepCateId) == false) {
					stepCategorySet.add(item.stepCateId)
					uniqueStepCategories.push({
						stepCateId: item.stepCateId,
						cateName: item.stepCateName,
						createTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
					})
				}
			})

			stepCategories.value = uniqueStepCategories
			console.log('工序分类加载成功(动态提取):', stepCategories.value)
		} catch (error) {
			console.error('加载工序分类失败:', error)
			uni.showToast({
				title: '加载工序分类失败',
				icon: 'none'
			})
		}
	}

	// 加载清单列表
	const loadChecklists = async (stepCateId: string): Promise<void> => {
		try {
			loading.value = true

			// TODO: 构建API调用参数
			// let apiInventoryName: string | null = null
			// let apiCateId: string | null = null
			// let apiStepId: string | null = null
			// let apiStepCateId: string | null = null
			//
			// // 优先使用筛选参数，其次使用原有的stepCateId参数
			// if (inventoryName != null && inventoryName !== '') {
			//     apiInventoryName = inventoryName
			// }
			// if (cateId != null && cateId !== '') {
			//     apiCateId = cateId
			// }
			// if (stepId != null && stepId !== '') {
			//     apiStepId = stepId
			// }
			// if (stepCateIdParam != null && stepCateIdParam !== '') {
			//     apiStepCateId = stepCateIdParam
			// } else if (stepCateId != null && stepCateId !== '') {
			//     apiStepCateId = stepCateId
			// }

			// TODO: 调用真实API接口
			// const response = await getInventoryList(apiInventoryName, apiCateId, apiStepId, apiStepCateId)
			// if (response.code === 200 && response.data != null) {
			//     // 将API数据赋值给 mockChecklistData，作为数据源
			//     mockChecklistData.value = response.data as InventoryItem[]
			//     // 重新生成分组数据
			//     generateGroupedData()
			//     console.log('清单列表加载成功:', mockChecklistData.value)
			// } else {
			//     // API调用失败时使用模拟数据
			//     generateGroupedData()
			//     console.log('API调用失败，使用模拟数据:', mockChecklistData.value)
			// }

			// 使用模拟数据（开发阶段）
			// mockChecklistData 已经在初始化时设置，直接生成分组数据
			generateGroupedData()
			console.log('清单列表加载成功(模拟数据):', mockChecklistData.value)
		} catch (error) {
			console.error('加载清单列表失败:', error)
			uni.showToast({
				title: '加载清单列表失败',
				icon: 'none'
			})
		} finally {
			loading.value = false
		}
	}

	// 事件处理函数
	const handleBack = (): void => {
		uni.navigateBack()
	}

	const handleCategorySelect = (categoryId: string): void => {
		selectedCategoryId.value = categoryId
		console.log('选择工序分类:', categoryId)
		// 重新加载清单数据
		loadChecklists(categoryId)
	}

	const handleChecklistSelect = (item: InventoryItem): void => {
		const compositeKey = generateCompositeKey(item)
		const isSelected = selectedChecklistIds.value.includes(compositeKey)

		console.log('单项选择 - 清单项:', item.inventoryName)
		console.log('单项选择 - 组合键:', compositeKey)
		console.log('单项选择 - 当前状态:', isSelected ? '已选中' : '未选中')
		console.log('单项选择 - 当前选中ID列表:', selectedChecklistIds.value)

		if (isSelected) {
			// 如果已选中，则取消选择 - 使用 splice 方法
			const index = selectedChecklistIds.value.indexOf(compositeKey)
			if (index > -1) {
				selectedChecklistIds.value.splice(index, 1)
				console.log('单项选择 - ID数组删除成功，索引:', index)
			}

			// 从选中的清单数组中移除 - 使用 inventoryId 直接比较
			let removedCount = 0
			for (let i = selectedChecklists.value.length - 1; i >= 0; i--) {
				const currentItem = selectedChecklists.value[i]
				const isMatch = (
					currentItem.inventoryId === item.inventoryId &&
					currentItem.stepCateId === item.stepCateId &&
					currentItem.stepId === item.stepId &&
					currentItem.cateId === item.cateId
				)
				console.log('单项选择 - 比较对象:', currentItem.inventoryName, '目标对象:', item.inventoryName, '是否匹配:', isMatch)
				if (isMatch) {
					selectedChecklists.value.splice(i, 1)
					removedCount++
					console.log('单项选择 - 清单数组删除成功，索引:', i)
					break
				}
			}

			console.log('单项选择 - 已取消选择，删除数量:', removedCount)
			console.log('单项选择 - 取消后ID列表长度:', selectedChecklistIds.value.length)
			console.log('单项选择 - 取消后清单列表长度:', selectedChecklists.value.length)
		} else {
			// 如果未选中，则添加选择
			selectedChecklistIds.value.push(compositeKey)
			selectedChecklists.value.push(item)
			console.log('单项选择 - 已添加选择')
			console.log('单项选择 - 添加后ID列表:', selectedChecklistIds.value)
		}
		console.log('单项选择完成 - 总选中项数:', selectedChecklists.value.length)
	}

	// 应用筛选条件
	const applyFilterConditions = async (): Promise<void> => {
		try {
			const savedFilter = uni.getStorageSync('checklistFilterData')
			if (savedFilter != null && savedFilter !== '') {
				const filterData = JSON.parse(savedFilter as string) as UTSJSONObject
				console.log('应用筛选条件:', filterData)

				// 根据筛选条件重新加载数据
				const stepCateId = filterData['stepCateId'] as string
				const stepIds = filterData['stepIds'] as Array<string>
				const inventoryCateIds = filterData['inventoryCateIds'] as Array<string>
				const searchKeywordValue = filterData['searchKeyword'] as string

				// TODO: 构建筛选参数
				// let inventoryName: string | null = null
				// let cateId: string | null = null
				// let stepId: string | null = null
				// let stepCateIdParam: string | null = null
				//
				// if (searchKeywordValue != null && searchKeywordValue !== '') {
				//     inventoryName = searchKeywordValue
				// }
				// if (inventoryCateIds != null && inventoryCateIds.length > 0) {
				//     // 如果选择了多个清单分类，使用第一个（或者可以循环调用多次API）
				//     cateId = inventoryCateIds[0]
				// }
				// if (stepIds != null && stepIds.length > 0) {
				//     // 如果选择了多个工序，使用第一个（或者可以循环调用多次API）
				//     stepId = stepIds[0]
				// }
				// if (stepCateId != null && stepCateId !== '') {
				//     stepCateIdParam = stepCateId
				// }

				// TODO: 调用清单列表API，传入筛选参数
				// await loadChecklists('', inventoryName, cateId, stepId, stepCateIdParam)

				// 暂时使用默认加载，后续对接API时再传入筛选参数
				await loadChecklists('')

				// 显示筛选提示
				let filterCount = 0
				if (stepCateId != null && stepCateId !== '') filterCount = filterCount + 1
				if (stepIds != null) filterCount = filterCount + stepIds.length
				if (inventoryCateIds != null) filterCount = filterCount + inventoryCateIds.length
				if (searchKeywordValue != null && searchKeywordValue !== '') filterCount = filterCount + 1
			}
		} catch (error) {
			console.error('应用筛选条件失败:', error)
		}
	}

	// 检查分类选择状态的计算属性 - 使用组合键
	const getCategorySelectStatus = (items: InventoryItem[]): string => {
		if (items.length === 0) return 'none'

		const selectedCount = items.filter(item => {
			const compositeKey = generateCompositeKey(item)
			return selectedChecklistIds.value.includes(compositeKey)
		}).length

		if (selectedCount === 0) return 'none'
		if (selectedCount === items.length) return 'all'
		return 'partial'
	}

	// 检查单个清单项是否被选中 - 用于模板中的状态判断
	const isItemSelected = (item: InventoryItem): boolean => {
		const compositeKey = generateCompositeKey(item)
		const isSelected = selectedChecklistIds.value.includes(compositeKey)
		// console.log('检查选中状态 - 清单项:', item.inventoryName, '组合键:', compositeKey, '是否选中:', isSelected)
		return isSelected
	}

	// 处理分类全选 - 使用组合键确保独立性
	const handleStepSelectAll = (stepItems: InventoryItem[]): void => {
		console.log('全选操作 - 当前分类清单项数量:', stepItems.length)
		console.log('全选操作 - 清单项组合键:', stepItems.map(item => generateCompositeKey(item)))

		// 检查当前分类下的所有项是否都已选中（使用组合键）
		const allSelected = stepItems.every(item => {
			const compositeKey = generateCompositeKey(item)
			return selectedChecklistIds.value.includes(compositeKey)
		})
		console.log('全选操作 - 当前状态:', allSelected ? '全部已选中' : '未全部选中')

		if (allSelected) {
			// 如果全部选中，则取消全部选择 - 使用 filter 方法
			const keysToRemove = stepItems.map(item => generateCompositeKey(item))
			selectedChecklistIds.value = selectedChecklistIds.value.filter(id => !keysToRemove.includes(id))
			selectedChecklists.value = selectedChecklists.value.filter(checklist => {
				const compositeKey = generateCompositeKey(checklist)
				return !keysToRemove.includes(compositeKey)
			})
			console.log('全选操作 - 已取消选择当前分类的所有项')
		} else {
			// 如果未全部选中，则选中全部
			stepItems.forEach(item => {
				const compositeKey = generateCompositeKey(item)
				if (selectedChecklistIds.value.includes(compositeKey) == false) {
					selectedChecklistIds.value.push(compositeKey)
					selectedChecklists.value.push(item)
				}
			})
			console.log('全选操作 - 已选中当前分类的所有项')
		}
		console.log('全选操作完成 - 总选中项数:', selectedChecklists.value.length)
	}



	// 筛选弹窗相关处理函数
	const handleFilter = (): void => {
		console.log('打开筛选弹窗')
		showFilterModal.value = true
	}

	const handleFilterModalClose = (): void => {
		console.log('关闭筛选弹窗')
		showFilterModal.value = false
	}

	const handleFilterModalConfirm = (filterData: any): void => {
		console.log('确认筛选条件:', filterData)

		// 保存筛选条件到存储
		uni.setStorageSync('checklistFilterData', JSON.stringify(filterData))

		// 应用筛选条件
		applyFilterConditions()

		// 关闭弹窗
		showFilterModal.value = false

		// 显示成功提示
		let filterCount = 0
		// 将any类型转换为UTSJSONObject
		const filterObj = filterData as UTSJSONObject
		const stepCateId = filterObj['stepCateId'] as string
		const stepIds = filterObj['stepIds'] as Array<string>
		const inventoryCateIds = filterObj['inventoryCateIds'] as Array<string>
		const searchKeywordValue = filterObj['searchKeyword'] as string

		if (stepCateId != null && stepCateId !== '') filterCount = filterCount + 1
		if (stepIds != null) filterCount = filterCount + stepIds.length
		if (inventoryCateIds != null) filterCount = filterCount + inventoryCateIds.length
		if (searchKeywordValue != null && searchKeywordValue !== '') filterCount = filterCount + 1

	}

	const handleConfirm = (): void => {
		if (selectedChecklists.value.length === 0) {
			uni.showToast({
				title: '请至少选择一个清单',
				icon: 'none'
			})
			return
		}

		// 提取原始的 inventoryId 列表，保持字符串类型避免精度丢失
		const inventoryIds = selectedChecklists.value.map(item => {
			// 保持字符串类型，避免大整数精度丢失
			return item.inventoryId
		})

		// 创建符合后端API格式的数据结构
		const selectedChecklistsData = {
			// 选中的清单ID列表（字符串类型，避免精度丢失）
			inventoryIds: inventoryIds,
			// 选中的完整清单项信息（用于前端显示）
			selectedItems: selectedChecklists.value.map(item => ({
				inventoryId: item.inventoryId,
				inventoryName: item.inventoryName,
				cateName: item.cateName,
				stepName: item.stepName,
				stepCateName: item.stepCateName,
				// 添加组合键用于前端识别
				compositeKey: generateCompositeKey(item)
			}))
		}

		console.log('确认提交 - 清单选择数据:', selectedChecklistsData)
		console.log('确认提交 - inventoryIds (字符串格式):', inventoryIds)
		console.log('确认提交 - 选中清单数量:', inventoryIds.length)

		// 通过存储传递数据给任务创建页面
		uni.setStorageSync('selectedChecklistsData', JSON.stringify(selectedChecklistsData))

		// 显示成功提示
		uni.showToast({
			title: `已选择${inventoryIds.length}个清单`,
			icon: 'success'
		})

		// 返回上一页
		uni.navigateBack()
	}



	// 页面生命周期
	onMounted(() => {
		console.log('清单选择页面加载')
		// 加载清单列表（会自动生成分组数据和工序分类）
		loadChecklists('')
		// 加载工序分类（从分组数据中提取）
		loadStepCategories()
		// 应用筛选条件（如果有的话）
		applyFilterConditions()
	})
</script>

<style scoped>
	.app-scroll {
		flex:1;
		background-color: #f5f5f5;
	}

	.checklist-select-container {
		width: 100%;
		background-color: #f7f8fa;
		flex-direction: column;
		padding-bottom: 140rpx;
	}

	/* Web端滚动容器 */
	.web-scroll-container {
		height: 100%;
		background-color: #f7f8fa;
	}

	/* 顶部导航栏 */
	.nav-bar {
		background-color: #FFFFFF;
		height: 88rpx;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.nav-left {
		width: 80rpx;
		height: 88rpx;
		align-items: center;
		justify-content: center;
	}

	.back-icon {
		font-size: 40rpx;
		color: #333333;
		font-weight: bold;
	}

	.nav-center {
		flex: 1;
		align-items: center;
		justify-content: center;
	}

	.nav-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}

	.nav-right {
		width: 80rpx;
		height: 88rpx;
		align-items: center;
		justify-content: center;
	}

	.nav-link {
		font-size: 28rpx;
		color: #1976D2;
	}

	/* 搜索栏 */
	.search-container {
		background-color: #FFFFFF;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.search-box {
		background-color: #f8f8f8;
		border-radius: 40rpx;
		height: 70rpx;
		flex-direction: row;
		align-items: center;
		padding: 0 30rpx;
	}

	.search-icon {
		font-size: 28rpx;
		color: #999999;
		margin-right: 20rpx;
	}

	.search-input {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}

	/* 工序分类标签 */
	.category-tabs {
		background-color: #FFFFFF;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.tabs-scroll {
		height: 80rpx;
	}

	.tabs-container {
		flex-direction: row;
		align-items: center;
		padding: 0 30rpx;
		height: 80rpx;
	}

	.tab-item {
		padding: 10rpx 30rpx;
		margin-right: 20rpx;
		border-radius: 40rpx;
		background-color: #f8f8f8;
		align-items: center;
		justify-content: center;
		min-width: 120rpx;
	}

	.tab-item.active {
		background-color: #1976D2;
	}

	.tab-text {
		font-size: 26rpx;
		color: #666666;
		white-space: nowrap;
	}

	.tab-item.active .tab-text {
		color: #FFFFFF;
	}

	/* 清单列表 */
	.checklist-container {
		flex: 1;
		padding: 20rpx 30rpx;
	}

	.loading-container,
	.empty-container {
		flex: 1;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}

	.loading-text,
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}

	.checklist-list {
		flex-direction: column;
	}

	/* 一级分类样式 */
	.category-group {
		margin-bottom: 40rpx;
	}

	.category-title {

		padding: 20rpx 0rpx;
		border-radius: 12rpx 12rpx 0 0;
		margin-bottom: 2rpx;
		display: flex;
    flex-direction: row;
    align-items: center;
	}
	.step-card-item{
		background: #fff;
    border-radius: 15rpx;
	}
	.category-card{
		width: 10rpx;
		height: 30rpx;
		background-image: linear-gradient(to bottom,#0081E3,#60BBFF);
	}
	.category-title-text {
		font-size: 32rpx;
		color: #131313;
		font-weight: bold;
		margin-left: 10rpx;
	}

	/* 二级分类样式（工序名称） */
	.step-main-group {
		background-color: #FFFFFF;
		border-radius: 0 0 12rpx 12rpx;
		overflow: hidden;
	}

	.step-main-title {
		padding: 20rpx 30rpx 35rpx;
	}
	.step-main-title-text {
		font-size: 30rpx;
		color: #131313;
		font-weight: bold;
	}

	/* 二级分类样式 */
	.step-group {
		background-color: #FFFFFF;
		border-radius: 0 0 12rpx 12rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
	}

	.step-title {
		padding: 10rpx 30rpx;
		margin: 0 30rpx;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		background-color: #F7F8FA;
		border-radius: 10rpx;
	}

	.step-title-text {
		font-size: 28rpx;
		color: #0189F6;
		font-weight: bold;
	}

	.step-select-all {
		flex-direction: row;
		align-items: center;
		padding: 8rpx 20rpx;
		border-radius: 20rpx;
	}

	.select-all-check {
		width: 32rpx;
		height: 32rpx;
		border-radius: 6rpx;
		border: 2rpx solid #d0d0d0;
		background-color: #FFFFFF;
		align-items: center;
		justify-content: center;
		margin-right: 12rpx;
	}

	.select-all-check.checked {
		background-color: #0189F6;
		border-color: #0189F6;
	}

	.select-all-check.partial {
		background-color: #0189F6;
		border-color: #0189F6;
	}

	.select-all-text {
		font-size: 24rpx;
		color: #0189F6;
	}

	.check-icon {
		font-size: 18rpx;
		color: #FFFFFF;
		font-weight: bold;
	}

	.partial-icon {
		font-size: 20rpx;
		line-height: 1;
	}

	/* 清单项样式 */
	.checklist-item {
		padding: 20rpx 30rpx 20rpx 50rpx;
		flex-direction: row;
		align-items: center;
		border-bottom: 1rpx solid #f0f0f0;
	}

	/* 移除不支持的 :last-child 选择器 */

	.item-check {
		width: 36rpx;
		height: 36rpx;
		border-radius: 8rpx;
		border: 2rpx solid #d0d0d0;
		background-color: #FFFFFF;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}

	.item-check.checked {
		background-color: #1976D2;
		border-color: #1976D2;
	}

	.check-icon {
		font-size: 20rpx;
		color: #FFFFFF;
		font-weight: bold;
	}

	.item-name {
		font-size: 28rpx;
		color: #333333;
		flex: 1;
		line-height: 1.4;
	}

	/* 底部确认按钮样式 */
	.bottom-confirm-container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		border-top: 1rpx solid #f0f0f0;
		padding: 20rpx 30rpx;
		padding-bottom: 40rpx;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		z-index: 999;
	}

	.confirm-info {
		flex: 1;
	}

	.selected-count {
		font-size: 28rpx;
		color: #666666;
	}

	.confirm-button {
		background-color: #007AFF;
		border-radius: 8rpx;
		padding: 24rpx 40rpx;
		align-items: center;
		justify-content: center;
		min-width: 200rpx;
	}

	.confirm-button.disabled {
		background-color: #cccccc;
	}

	.confirm-text {
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: bold;
	}
</style>
