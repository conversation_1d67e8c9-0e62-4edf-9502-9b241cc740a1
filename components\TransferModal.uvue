<template>
	<!-- 半透明遮罩层 -->
	<view class="mask" v-if="show" @click="closeModal"></view>
	
	<!-- 主弹窗容器 -->
	<view v-if="show" class="modal-container">
		<view class="modal-title-container">
				<text class="iconfont icon_font contentMsg">&#xeafd;</text>
			<text class="modal-title">转办信息</text>
		</view>
		
		<!-- 选择转办人 -->
		<view class="form-item">
			<view class="picker-box" @click="handleSelectUser">
				<text v-if="loading">加载中...</text>
				<text v-else-if="selectedUser != null">{{ selectedUser.nickName }}</text>
				<text class="placeholder" v-else>请选择转办人</text>
				<text class="iconfont icon_font">&#xe747;</text>


			</view>
		</view>
		
		<!-- 填写备注 -->
		<view class="form-item">
			<text class="form-label">填写备注：</text>
			<textarea 
				v-model="remark" 
				class="remark-textarea" 
				placeholder="请输入备注信息"
				:maxlength="200"
			></textarea>
		</view>
		
		<!-- 按钮组 -->
		<view class="button-group">
			<button class="btn cancel-btn" @click="closeModal">取消</button>
			<button class="btn confirm-btn" @click="handleConfirm">确认</button>
		</view>
	</view>
	
	<!-- 转办人选择器 -->
	<select-picker
		:show="showUserPicker"
		:items="pickerItems"
		:option="selectedIndex"
		@close="handlePickerClose"
		@confirm="handlePickerConfirm"
	/>
</template>

<script setup>
	import { ref, computed, watch } from 'vue'
	import { TransferUser, TransferRequest, TransferModalData, ProjectOption, confirmHcType } from '@/utils/apiType.uts'
	// import { getTransferUserList } from '@/common/api.uts'
	import selectPicker from '@/components/select_picker.uvue'
	
	// 组件props
	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		},
		taskUserId: {
			type: String,
			default: ''
		}
	})
	
	// 组件emits
	const emit = defineEmits(['close', 'confirm'])
	
	// 响应式数据
	const loading = ref<boolean>(false)
	const error = ref<string>('')
	const transferUsers = ref<TransferUser[]>([])
	const selectedUser = ref<TransferUser | null>(null)
	const remark = ref<string>('')
	const showUserPicker = ref<boolean>(false)
	const selectedIndex = ref<number[]>([0])
	
	// 模拟转办人数据（基于API响应示例）
	const mockTransferUsers: TransferUser[] = [
		{ userId: "1955435055660666881", nickName: "郝志新9" },
		{ userId: "1955435275832266754", nickName: "郝志新8" },
		{ userId: "1955435633920970754", nickName: "郝志新7" },
		{ userId: "1955435744123456789", nickName: "李工程师" },
		{ userId: "1955435855987654321", nickName: "王主管" }
	]
	
	// 获取转办人列表
	const fetchTransferUsers = (): void => {
		if (props.taskUserId === '' || props.taskUserId === '0') {
			// 使用模拟数据
			transferUsers.value = mockTransferUsers
			return
		}
		
		loading.value = true
		error.value = ''
		
		// 实际API调用（暂时使用模拟数据）
		setTimeout(() => {
			transferUsers.value = mockTransferUsers
			loading.value = false
		}, 500)
		
		// 真实API调用代码（注释掉，等后端接口可用时启用）
		/*
		getTransferUserList(props.taskUserId.toString())
			.then((response: any) => {
				if (response.code === 200) {
					transferUsers.value = response.data as TransferUser[]
				} else {
					error.value = response.msg || '获取转办人列表失败'
				}
			})
			.catch((err: any) => {
				error.value = '网络请求失败'
				console.error('获取转办人列表失败:', err)
			})
			.finally(() => {
				loading.value = false
			})
		*/
	}

	// 重置表单
	const resetForm = (): void => {
		selectedUser.value = null
		remark.value = ''
		error.value = ''
		showUserPicker.value = false
		selectedIndex.value = [0]
	}

	// 转换为picker组件需要的格式
	const pickerItems = computed((): ProjectOption[] => {
		return transferUsers.value.map((user: TransferUser): ProjectOption => ({
			label: user.nickName,
			value: user.userId
		}))
	})

	// 监听弹窗显示状态
	watch(function(): boolean {
		return props.show
	}, function(newVal: boolean) {
		if (newVal) {
			fetchTransferUsers()
		} else {
			resetForm()
		}
	})

	// 选择转办人
	const handleSelectUser = (): void => {
		if (loading.value) {
			uni.showToast({
				title: '数据加载中，请稍候',
				icon: 'none',
				duration: 1500
			})
			return
		}
		
		
		showUserPicker.value = true
	}
	
	// 关闭用户选择器
	const handlePickerClose = (): void => {
		showUserPicker.value = false
	}
	
	// 确认选择用户
	const handlePickerConfirm = (data: confirmHcType): void => {
		if (data.selectedItem != null && data.index != null) {
			const selectedItem = data.selectedItem
			const userId = selectedItem.value as string
			const user = transferUsers.value.find((u: TransferUser) => u.userId === userId)
			if (user != null) {
				selectedUser.value = user
				selectedIndex.value = [data.index]
			}
		}
		showUserPicker.value = false
	}

	// 关闭弹窗
	const closeModal = (): void => {
		emit('close')
	}

	// 确认转办
	const handleConfirm = (): void => {
		if (selectedUser.value == null) {
			uni.showToast({
				title: '请选择转办人',
				icon: 'none',
				duration: 1500
			})
			return
		}

		const transferData: TransferRequest = {
			taskUserId: props.taskUserId,
			receive_user: selectedUser.value.userId,
			remark: remark.value
		}

		emit('confirm', transferData)
	}
</script>

<style scoped>
	.mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 998;
	}
	
	.modal-container {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 80%;
		max-width: 600rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
		padding: 32rpx;
		z-index: 999;
	}
	
	.modal-title-container {
		text-align: center;
		margin-bottom: 40rpx;
		display: flex;
    flex-direction: row;
    align-items: center;
	}
	.contentMsg{
		color:#1976D2;
		font-size: 	42rpx;
		margin-right: 10rpx;
	}
	.modal-title {
		font-size: 34rpx;
		font-weight: bold;
		color: #000;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.form-label {
		font-size: 30rpx;
		color: #131313;
		margin-bottom: 16rpx;
		/* display: block; */
	}
	
	.picker-box {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		min-height: 80rpx;
		border-bottom: 1px solid #E0E3E5;
	}
	
	.placeholder {
		color: #999;
	}
	
	.arrow-icon {
		color: #666;
		font-size: 28rpx;
	}
	
	.remark-textarea {
		width: 100%;
		min-height: 120rpx;
		padding: 20rpx;
		background-color: #F3F3F3;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #131313;
	}
	
	.button-group {
		flex-direction: row;
		justify-content: space-between;
		margin-top: 40rpx;
	}
	
	.btn {
		width: 45%;
		height: 80rpx;
		border-radius: 44rpx;
		font-size: 30rpx;
		border: none;
	}
	
	.cancel-btn {
		background-color: #EFEFEF;
		color: #131313;
	}
	
	.confirm-btn {
		background-color: #0189F6;
		color: #fff;
	}
</style>
