<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		 <view class="container">
			 <view class="form-section">
				<view class="section-title">
					<image class="title_icon" src="/static/image/grxx.png" mode=""></image>
					<text>个人基本信息</text>
				</view> 
				<view class="form_div">
					<text class="form_label">姓名：</text>
					<input
						v-model="registerFrom.nickName"
						placeholder="请输入姓名" 
						placeholder-class="placeholder"
					
					/>
				</view>
				<view class="form_div" style="justify-content: space-between;">
					<text class="form_label">性别：</text>
					<radio-group  @change="radioChange" class="uni-row">
					  <radio :value="1" :checked="registerFrom.sex==1" class="radio">男</radio>
					  <radio :value="2" :checked="registerFrom.sex==2" class="radio">女</radio>
					</radio-group>
				</view>
				<view class="form_div">
					<text class="form_label">工号：</text>
					<input
					v-model="registerFrom.workNum"
						placeholder="请输入工号" 
						placeholder-class="placeholder"
					
					/>
				</view>
				<view class="form_div">
					<text class="form_label">所属项目部：</text>
					<view class="picker-box" @click="hanldeshow(1)">
					   <text v-if="selectActive!=null">{{ selectActive.label  }}</text>
					   <text class="weiSelect" v-else> 请选择项目部</text>
					 </view>
					<selectPicker v-if="showPicker"  :show="showPicker" @close="pickerColse" @confirm="pickerOpen" :items="items" :option="selectedIndex"></selectPicker>
				</view>
				<view class="form_div">
					<text class="form_label">任职部门：</text>
					<uni-data-picker style="width: 80%;"  placeholder="请选择任职部门" v-model="dept" :localdata="deptList" @change="onchange" ></uni-data-picker>
				</view>
				<view class="form_div">
					<text class="form_label">岗位：</text>
					<view class="picker-box" @click="hanldeshow(2)">
					   <text v-if="post!=null">{{ post.label  }}</text>
					   <text class="weiSelect" v-else> 请选择岗位</text>
					 </view>
					<selectPicker v-if="showPicker"  :show="showPicker" @close="pickerColse" @confirm="pickerOpen" :items="items" :option="selectedIndex"></selectPicker>
				</view>
				
			</view>
			<view class="form-section">
				<view class="section-title">
					<image class="title_icon" src="/static/image/grzhxx.png" mode=""></image>
					<text>个人账号信息</text>
				</view> 
				<view class="form_div">
					<text class="form_label">手机号：</text>
					<input
						style="width: 80%;"
						v-model="registerFrom.phone"
						placeholder="请输入手机号" 
						placeholder-class="placeholder"
						type="number"
						maxlength="11"	
					
					/>
				</view>
				<view class="form_div">
					<text class="form_label">验证码：</text>
					<input
						class="input-field" 
						v-model="registerFrom.yzm"
						placeholder="请输入验证码" 
						placeholder-class="placeholder"
						type="number"
						maxlength="6"	
					
					/>
					<view class="yzm_div" :class="{'disabledys':codeDisabled==true}"
						@click="getCode">
						<text class="color_font" :class="{'disabledys':codeDisabled==true}"> {{ codeText }}</text>
						
					</view>
				</view>
				<view class="form_div">
					<text class="form_label">新密码：</text>
					<input
						class="input-pwd" 
						v-model="registerFrom.pwd"
						placeholder="请输入新密码" 
						placeholder-class="placeholder"
						:password="newPwdVisible==true?false:true"
					
					/>
					<image class="uni-icon" :src="
					  !newPwdVisible
						? '/static/image/eye.png'
						: '/static/image/eye-active.png'
					" @click="changePassword(1)"></image>
					
				</view>
				<view class="form_div">
					<text class="form_label">确认密码：</text>
					<input
						style="width:71%;"
						v-model="registerFrom.confirmPwd"
						placeholder="请输入确认密码" 
						placeholder-class="placeholder"
						:password="confirmPwdVisible==true?false:true"
					
					/>
					<image class="uni-icon" :src="
					  !confirmPwdVisible
						? '/static/image/eye.png'
						: '/static/image/eye-active.png'
					" @click="changePassword(2)"></image>
				</view>
			</view>
			
			
			<button class="submit_btn" @click="submit">确认</button>
			
			<text  @click="clickGoLogin" class="yyzhqdl">已有账号，去登录</text>
			
		 </view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref } from 'vue'
	import {getDeptTree} from '@/common/api.uts'
	import {ProjectOption,confirmHcType,registerType} from '@/utils/apiType.uts'
	import selectPicker from '@/components/select_picker.uvue'
	
	
	const registerFrom=ref<registerType>({
		nickName:'',
		sex:null,
		workNum:'',
		tenantId:'',
		deptId:'',
		postId:'',
		phone:'',
		yzm:'',
		pwd:'',
		confirmPwd:'',
		
	})
	// 验证码按钮禁用状态
	const codeDisabled = ref<Boolean>(false)
	// 验证码倒计时
	const countdown=ref<number>(0)
	const timer=ref<number|null>(null)
	
	// 查看确认密码
	const confirmPwdVisible=ref<Boolean>(false)
	// 查看新密码
	const newPwdVisible=ref<Boolean>(false)
	// 验证码按钮文本
	const codeText = computed(() => {
	  return countdown.value > 0 ? `${countdown.value}秒后重新获取` : '获取验证码'
	})
	const validateMobile = (phone: string): boolean => {
	  const reg = /^1[3-9]\d{9}$/
	  return reg.test(phone)
	}
	// 当前点击的弹窗
	const currentSelect=ref<number|string|null>(null)
	
	const showPicker=ref(false)      // 控制选择器显示
	const items=ref<ProjectOption[]|null>(null)
	const selectedIndex=ref<number[]>([0])  // 选择的是第几个
	
	const sex=ref<null|number>(null)
	// 所属部门选择
	const selectActive=ref<ProjectOption|null>(null)
	// 岗位选择 
	const post=ref<ProjectOption|null>(null)
	// 任职部门
	const dept=ref<UTSJSONObject[]|null>(null)
	
	// 所属项目部
	const multiTenant=ref<ProjectOption[]>([               // 可选项数据
		{ value: 11, label: '选项一' },
		{ value: 22, label: '选项二' },
		{ value: 33, label: '选项三' },
		{ value: 44, label: '选项四' },
		{ value: 55, label: '选项五' }
	])
	
	// 任职部门
	const deptList=ref<UTSJSONObject[]>([{
		text: "一年级",
		value: "1",
		children: [{
			text: "1.1班",
			value: "2"
		},
		{
			text: "1.2班",
			value: "3"
		}]
	},
	{
		text: "二年级",
		value: "4",
		children: [{
			text: "2.1班",
			value: "5"
		},
		{
			text: "2.2班",
			value: "6"
		}]
	},
	])
	// 岗位
	const postList=ref<ProjectOption[]>([
		{ value: 11, label: '选项一' },
		{ value: 22, label: '选项二' },
		{ value: 33, label: '选项三' },
		{ value: 44, label: '选项四' },
		{ value: 55, label: '选项五' }
	])
	
	const radioChange=(e:UniRadioGroupChangeEvent)=>{
		registerFrom.value.sex=parseInt(e.detail.value as string)
	}
	
	//打开弹窗选择
	const hanldeshow=(i:number)=>{
		console.log('打开弹窗');
		currentSelect.value=i
		if(i==1){
			if(selectActive.value!=null){
				const index = multiTenant.value.findIndex(item => item.value == selectActive.value?.value);
				
				selectedIndex.value=[index]

			}
			items.value=multiTenant.value
		}else if(i==2){
			if(post.value!=null){
				const index = postList.value.findIndex(item => item.value == post.value?.value);
				
				selectedIndex.value=[index]
			
			}else
			items.value=postList.value
		}
		showPicker.value=true
	}
	// 取消选择组件
	const pickerColse=()=>{
		
		showPicker.value=false
	}
	// 选择确认组件
	const pickerOpen=(data:confirmHcType)=>{
		if(currentSelect.value==1){
			selectActive.value=data.selectedItem
		}else if(currentSelect.value==2){
			post.value=data.selectedItem
		}
		
		
		
		// selectedProject.value=data.label
		showPicker.value=false
		
	}
	
	
	// 清理定时器
	const clearTimer = () => {
	  if (timer.value !== null) {
	    const currentTimer = timer.value
	    clearInterval(currentTimer)
	    timer.value = null
	  }
	  codeDisabled.value = false
	  countdown.value = 0
	}
	// 获取验证码
	const getCode=()=>{
		if (codeDisabled.value) return
		if(registerFrom.value.phone==''){
			uni.showToast({ title: '请填写手机号', icon: 'none' })
			return
		}
		// 将手机号转换为字符串（使用toString方法）
		if(registerFrom.value.phone!=null){
			const phone = registerFrom.value.phone.toString()
			const iszj:Boolean= validateMobile(phone)
			
			if(iszj==false){
				uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
				return
			}
		}
		
		uni.showToast({ title: '验证码已发送' })
		
		// 启用禁用状态并开始倒计时
		codeDisabled.value = true
		countdown.value = 60
		  
			 // 清除之前的定时器
	   if (timer.value !== null) {
			const currentTimer = timer.value
			clearInterval(currentTimer)
			timer.value = null
	   }
			
		// 创建新的定时器并保存引用
		timer.value = setInterval(() => {
			if (countdown.value <= 1) {
			  clearTimer()
			  codeDisabled.value = false
			  countdown.value = 0
			  
			  
			  return
			}
			
			countdown.value--
		}, 1000)
			
			
	}
	
	// 点击可查看密码
	const changePassword=(i:number)=>{
		if(i==1){
			newPwdVisible.value=!newPwdVisible.value
		}else{
			confirmPwdVisible.value=!confirmPwdVisible.value
		}
		
	}
	
	// 提交
	const submit=()=>{
		console.log('提交');
		
		
		
		
		if(registerFrom.value.nickName==''){
			uni.showToast({ title: '请填写姓名', icon: 'none' })
			return
		}
		if(registerFrom.value.sex==null){
			uni.showToast({ title: '请选中性别', icon: 'none' })
			return
		}
		if(registerFrom.value.workNum==''){
			uni.showToast({ title: '请填写工号', icon: 'none' })
			return
		}
		if(selectActive.value==null){
			uni.showToast({ title: '请选择项目部', icon: 'none' })
			return
		}
		registerFrom.value.tenantId=selectActive.value.value
		if(registerFrom.value.deptId==''){
			uni.showToast({ title: '请选择任职部门', icon: 'none' })
			return
		}
		if(post.value==null){
			uni.showToast({ title: '请选择岗位', icon: 'none' })
			return
		}
		registerFrom.value.postId=post.value.value
		if(registerFrom.value.phone==''){
			uni.showToast({ title: '请填写手机号', icon: 'none' })
			return
		}
		// 将手机号转换为字符串（使用toString方法）
		if(registerFrom.value.phone!=null){
			const phone = registerFrom.value.phone.toString()
			const iszj:Boolean= validateMobile(phone)
			
			if(iszj==false){
				uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
				return
			}
		}
		
		if(registerFrom.value.yzm==''){
			uni.showToast({ title: '请填写验证码', icon: 'none' })
			return
		}
		
		if(registerFrom.value.pwd==''){
			uni.showToast({ title: '请填写新密码', icon: 'none' })
			return
		}
		if(registerFrom.value.confirmPwd==''){
			uni.showToast({ title: '请填写确认密码', icon: 'none' })
			return
		}
		if(registerFrom.value.pwd!= registerFrom.value.confirmPwd){
			uni.showToast({ title: '两次密码输入不一致', icon: 'none' })
			return
		}
		uni.showLoading({ title: '提交中...' })
		setTimeout(() => {
		    uni.hideLoading()
		    uni.showToast({ title: '注册成功' })
		    
		    setTimeout(() => {
		      uni.navigateBack()
		    }, 1500)
		}, 2000)
		
	}
	// 去登录
	const clickGoLogin=()=>{
		uni.reLaunch ({
			url:'/pages/login/index'
		})
	}
	
	// 任职部门选择
	const onchange=(e:UTSJSONObject[])=>{
		dept.value=e
		registerFrom.value.deptId=e[e.length-1].value as string
	}
	
	
	onLoad(()=>{
		
	})
</script>

<style lang="scss" scoped>
.container {
	// padding: 24rpx 32rpx;
	background-color: #FFFFFF;
}
.form-section {
  margin-bottom: 48rpx;
}
.title_icon{
	width: 40rpx;
	height: 40rpx;
	margin-right: 10rpx;
}
.section-title{
	display: flex;
	flex-direction: row;
	align-items: center;
	font-weight: bold;
	font-size: 34rpx;
	background: #F7F8FA;
	padding: 40rpx;
}
.form_div{
	display: flex;
	flex-direction: row;
	align-items: center;
	// justify-content: space-between;
	width: 100%;
	padding: 40rpx 40rpx;
	border-bottom: 1rpx solid #e5e5e5;
}
.form_label{
	color: #353535;
	font-size:30rpx;
	
}
.radio{
	margin-right: 20rpx;
}
.weiSelect{
	color: #666;
}
.picker-box {
  display: flex;
  
  font-size: 26rpx;
  
}
.input-field{
	width: 48%;
}
.input-pwd{
	width: 75%;
}
.yzm_div{
	background: #FFFFFF;
	border-radius: 30rpx;
	border: 1rpx solid #91CEFF;
	color: #0189F6;
	padding: 15rpx;
	color:#131313;
	font-size: 26rpx;
	
}
.color_font{
	color: #0189F6;
}
.disabledys {
  background-color: #f0f2f5 !important;
  color: #c8c8c8 !important;
}
.uni-icon{
	width: 40rpx;
	height: 40rpx;
}
.submit_btn{
	width: 90%;
	margin: 0 auto;
	background: #0189F6;
	border-radius: 0rpx 0rpx 0rpx 0rpx;
	border: 1rpx solid #0189F6;
	color:#fff;
	border-radius: 40rpx;
	margin-top: 40rpx;
}
.yyzhqdl{
	width: 100%;
	text-align: center;
	margin: 20rpx 0;
	color: #666666;
	font-size: 34rpx;
	// display: flex;
	// justify-content: center;
	// text-align: center;
}

</style>
