<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="details_content">
			 <view class="step-container">
				<view class="step-item" v-for="(item,index) in entryDetailsList" :class="{'last_child':index==entryDetailsList.length-1}">
					<view class="step-icon-container">
						<view class="step-circle" :class="{'active':item.status!=1}">
							<text class="iconfont icon_font">&#xe616;</text>
							<image v-if="item.status!=1" class="xuanzhong" src="/static/image/xz.png" mode=""></image>
						</view>
						<view class="step-line"  :class="{'last_child_line':index==entryDetailsList.length-1}"></view>
					</view>
					<view class="step-content">
						<view class="step_header">
							<view class="setp_header_left">
								<text class="step-title">
									{{item.title}}
									<text v-if="item.name!=''">({{item.name}})</text>
								</text>
							</view>
							<view class="setp_header_right">
								<text class="dsp" v-if="item.status==1">待审批</text>
								<text class="ytg" v-else-if="item.status==2">已通过</text>
								<text class="ybh" v-else-if="item.status==3">已驳回</text>
							</view>
						</view>
						
						<text class="step-time">{{item.time}}</text>
						<view class="bhyy" v-if="item.status==3">
							<text class="ybh">驳回原因：</text>
							<text class="bhyy_content ybh">{{item.rejectContent}}</text>
						</view>
					</view>
				</view>
			 </view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref } from 'vue'
	import {entryDeatilsType} from '@/utils/apiType.uts'
	// 1 待审批 2 已审批 3已驳回
	// 入职详情内容
	const entryDetailsList=ref<entryDeatilsType[]>([
		{title:'提交个人信息',name:'',time:'2025.03.02',status:2,rejectContent:'',id:1},
		{title:'办公室审批',name:'张某某',time:'2025.03.02',status:2,rejectContent:'',id:2},
		{title:'领导审批',name:'李某某',time:'2025.03.02',status:3,rejectContent:'驳回原因驳回原因驳回原因驳回原因驳回原因',id:3},
		{title:'岗位分配',name:'李某某',time:'2025.03.02',status:1,rejectContent:'',id:4},
	])
</script>

<style lang="scss" scoped> 
.details_content{
	padding: 40rpx;
	height: 100%;
	background-color: #f9fafb;
}
.step-container {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx 0rpx 24rpx;
}
.step-item {
  display: flex;
  flex-direction: row;
  position: relative;
}

.step-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  
  .step-circle {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50rpx;
    z-index: 2;
	border: 1rpx solid #d5d5d6;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	background-color: #eeeeee;
	 overflow: visible;
	position: relative;
	
    
    &.active {
      background-color: #0189F6;
	  .icon_font{
		  color: #fff;
	  }
	  
    }
    
    &.pending {
      width: 32rpx;
      height: 32rpx;
      border: 8rpx solid #e1e5eb;
      background: #ffffff;
    }
  }
  
  .step-line {
    flex: 1;
    width: 2rpx;
	// height: 120rpx;
    background-color: #d5d5d6;
    margin: 10rpx 0;
  }
}

.step-content {
  flex: 1;
  padding: 0 24rpx 24rpx;
  border-bottom: 2rpx dashed #e9eaec;
  margin-bottom: 40rpx;
  
  .step-title {
    font-size: 32rpx;
    color: #131313;
    margin-bottom: 8rpx;
    
  }
  
  .step-time {
    font-size: 26rpx;
    color: #131313;
    margin-bottom: 16rpx;
  }
  
  .status-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 16rpx;
  }
  
  .status-text {
    font-size: 28rpx;
    padding: 2rpx 10rpx;
    
    &.approved {
      color: #00b42a;
      background-color: rgba(0, 180, 42, 0.1);
      border-radius: 4rpx;
    }
    
    &.rejected {
      color: #f53f3f;
      background-color: rgba(245, 63, 63, 0.1);
      border-radius: 4rpx;
    }
    
    &.pending {
      color: #ff7d00;
      background-color: rgba(255, 125, 0, 0.1);
      border-radius: 4rpx;
    }
  }
  
  .reject-reason {
    font-size: 26rpx;
    color: #f53f3f;
    margin-top: 10rpx;
  }
  

}
.last_child{
	border-bottom: none;
}

.last_child_line {
	margin-bottom: 0;
	display: none;

}
.icon_font{
	font-size: 30rpx;
}

.xuanzhong{
	width: 30rpx;
	height: 30rpx;
	
	position: absolute;
	bottom: 0;
	right: 0;
	z-index: 222;
}
.step_header{
	width: 100%;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}
.setp_header_left{
	width: 65%;
}
.setp_header_right{
	width: 20%;
}
.ytg{
	color: #00B900;
}
.ybh{
	color:#B90000;
}
.dsp{
	color:#888888;
}
.bhyy{
	width: 100%;
	display: flex;
	flex-direction: row;
	
}
.bhyy_content{
	flex: 1;
}
</style>
