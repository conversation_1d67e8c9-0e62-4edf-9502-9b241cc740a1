<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="app-page">
			<view class="form_div">
				<text class="form_label">手机号：</text>
				<input
					class="input_content"
					v-model="editPwdInfo.phone"
					placeholder="请输入手机号" 
					placeholder-class="placeholder"
					type="number"
					maxlength="11"	
				
				/>
			</view>
			<view class="form_div">
				<text class="form_label">验证码：</text>
				<input
					class="input-field" 
					v-model="editPwdInfo.yzm"
					placeholder="请输入验证码" 
					placeholder-class="placeholder"
					type="number"
					maxlength="6"	
				
				/>
				<view class="yzm_div" :class="{'disabledys':codeDisabled==true}"
					@click="getCode">
					<text :class="{'disabledys':codeDisabled==true}"> {{ codeText }}</text>
					
				</view>
			</view>
			<view class="form_div">
				<text class="form_label">新密码：</text>
				<input
					class="input-pwd" 
					v-model="editPwdInfo.newPwd"
					placeholder="请输入新密码" 
					placeholder-class="placeholder"
					:password="newPwdVisible==true?false:true"
				
				/>
				<image class="uni-icon" :src="
				  !newPwdVisible
					? '/static/image/eye.png'
					: '/static/image/eye-active.png'
				" @click="changePassword(1)"></image>
				
			</view>
			<view class="form_div">
				<text class="form_label">确认新密码：</text>
				<input
					class="input-pwd" 
					v-model="editPwdInfo.confirmPwd"
					placeholder="请输入确认新密码" 
					placeholder-class="placeholder"
					:password="confirmPwdVisible==true?false:true"
				
				/>
				<image class="uni-icon" :src="
				  !confirmPwdVisible
					? '/static/image/eye.png'
					: '/static/image/eye-active.png'
				" @click="changePassword(2)"></image>
			</view>
			<button class="submit_btn" @click="submit">确认</button>
		</view>
		
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref } from 'vue'
	import {editPwd} from '@/utils/apiType.uts'
	
	const editPwdInfo=ref<editPwd>({
		phone:'',
		yzm:'',
		newPwd:'',
		confirmPwd:'',
	})
		
	 
	 // 验证码按钮禁用状态
	 const codeDisabled = ref<Boolean>(false)
	 // 验证码倒计时
	 const countdown=ref<number>(0)
	 const timer=ref<number|null>(null)
	 
	 // 查看确认密码
	 const confirmPwdVisible=ref<Boolean>(false)
	 // 查看新密码
	 const newPwdVisible=ref<Boolean>(false)
	 // 验证码按钮文本
	 const codeText = computed(() => {
	   return countdown.value > 0 ? `${countdown.value}秒后重新获取` : '获取验证码'
	 })
	 const validateMobile = (phone: string): boolean => {
	   const reg = /^1[3-9]\d{9}$/
	   return reg.test(phone)
	 }
	 // 清理定时器
	 const clearTimer = () => {
	   if (timer.value !== null) {
	     const currentTimer = timer.value
	     clearInterval(currentTimer)
	     timer.value = null
	   }
	   codeDisabled.value = false
	   countdown.value = 0
	 }
	 // 获取验证码
	 const getCode=()=>{
		if (codeDisabled.value) return
		if(editPwdInfo.value.phone==''){
			uni.showToast({ title: '请填写手机号', icon: 'none' })
			return
		}
		// 将手机号转换为字符串（使用toString方法）
		if(editPwdInfo.value.phone!=null){
			const phone = editPwdInfo.value.phone.toString()
			const iszj:Boolean= validateMobile(phone)
			
			if(iszj==false){
				uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
				return
			}
		}
		
		uni.showToast({ title: '验证码已发送' })
		
		 // 启用禁用状态并开始倒计时
		  codeDisabled.value = true
		  countdown.value = 60
		  
		 // 清除之前的定时器
	    if (timer.value !== null) {
			const currentTimer = timer.value
			clearInterval(currentTimer)
			timer.value = null
	    }
		
		// 创建新的定时器并保存引用
		timer.value = setInterval(() => {
		    if (countdown.value <= 1) {
		      clearTimer()
			  codeDisabled.value = false
			  countdown.value = 0
			  
			  
		      return
		    }
			
		    countdown.value--
		}, 1000)
		
		
	 }
	 
	 
	// 点击可查看密码
	const changePassword=(i:number)=>{
		if(i==1){
			newPwdVisible.value=!newPwdVisible.value
		}else{
			confirmPwdVisible.value=!confirmPwdVisible.value
		}
		
	}
	
	// 提交
	const submit=()=>{
		console.log('提交');
		if(editPwdInfo.value.phone==''){
			uni.showToast({ title: '请填写手机号', icon: 'none' })
			return
		}
		// 将手机号转换为字符串（使用toString方法）
		if(editPwdInfo.value.phone!=null){
			const phone = editPwdInfo.value.phone.toString()
			const iszj:Boolean= validateMobile(phone)
			
			if(iszj==false){
				uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
				return
			}
		}
		if(editPwdInfo.value.yzm==''){
			uni.showToast({ title: '请填写验证码', icon: 'none' })
			return
		}
		
		if(editPwdInfo.value.newPwd==''){
			uni.showToast({ title: '请填写新密码', icon: 'none' })
			return
		}
		if(editPwdInfo.value.confirmPwd==''){
			uni.showToast({ title: '请填写确认密码', icon: 'none' })
			return
		}
		if(editPwdInfo.value.newPwd!= editPwdInfo.value.confirmPwd){
			uni.showToast({ title: '两次密码输入不一致', icon: 'none' })
			return
		}
		// 模拟API请求
		uni.showLoading({ title: '提交中...' })
		setTimeout(() => {
		    uni.hideLoading()
		    uni.showToast({ title: '密码修改成功' })
		    
		    setTimeout(() => {
		      uni.navigateBack()
		    }, 1500)
		}, 2000)
	}
	 
</script>

<style lang="scss" scoped>
/* 修改scroll-view在APP平台下的高度 */
:deep(.uni-scroll-view) {
  height: 100%;
}
.app-page {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  height: 100%;
  padding-bottom: 40rpx;
}
.form_div{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 40rpx 40rpx;
	border-bottom: 1rpx solid #e5e5e5;
}
.form_label{
	color: #353535;
	font-size:30rpx;
}
.input_content{
	width: 80%;
}
.yzm_div{
	background: #FFFFFF;
	border-radius: 30rpx;
	border: 1rpx solid #707070;
	padding: 15rpx;
	color:#131313;
	font-size: 26rpx;
	
}
.disabledys {
  background-color: #f0f2f5 !important;
  color: #c8c8c8 !important;
}
.input-field{
	width: 48%;
}
.input-pwd{
	width: 60%;
}
.uni-icon{
	width: 40rpx;
	height: 40rpx;
}
.submit_btn{
	width: 90%;
	margin: 0 auto;
	position: absolute;
	left: 5%;
	bottom: 10%;
	background: #0189F6;
	border-radius: 0rpx 0rpx 0rpx 0rpx;
	border: 1rpx solid #0189F6;
	color:#fff;
}
</style>
