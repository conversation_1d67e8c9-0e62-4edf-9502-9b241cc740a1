
export function code401() {
	// 清除token等数据
	clearStorage()

	uni.showToast({
	  icon: "none", 
	  title:'鉴权失败'
	});
	// 跳转登录页
	goLoginPage()

	
}




// 跳转登录页
function goLoginPage() {

	setTimeout(() => {
		let curPage = getCurrentPages();
		let route = curPage[curPage.length - 1].route; //获取当前页面的路由
		if (route != "pages/login/index") {
			uni.navigateTo({
				url: '/pages/login/index',
				fail() {
					console.error("跳转失败")
				}
			});
		}
	},1500)
}

// 定义存储数据类型
export  type StorageDataType = {
  key: string ;
  value: string | Boolean; // 使用any允许存储任意类型值
}
// 更新saveStorage函数使用定义的类型
export function saveStorage(data: StorageDataType) {
  uni.setStorageSync(data.key, data.value)
}


// 存储数据
export function getStorage(key: string): any {
  return uni.getStorageSync(key) as any // 强制断言为 any
}
// 清除本地所有数据
export function clearStorage() {

	uni.clearStorageSync()
}
