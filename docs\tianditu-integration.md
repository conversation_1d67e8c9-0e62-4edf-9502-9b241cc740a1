# 天地图集成说明

## 概述

本项目已集成天地图逆地理编码功能，可以根据经纬度坐标获取详细的地址信息。

## 天地图API密钥

当前使用的天地图API密钥：`4552ec277a7690bf1e3b23cd1b6b2d63`

## 文件结构

### 1. 工具文件：`utils/tiandituUtils.uts`

这是天地图功能的核心工具文件，包含以下功能：

- **类型定义**：
  - `TiandituGeocodeResponse`：天地图API响应类型
  - `AddressResult`：地址解析结果类型

- **主要函数**：
  - `getAddressFromTianditu()`：调用天地图API进行逆地理编码
  - `formatAddress()`：格式化地址信息（完整版本，需要4个参数）
  - `formatAddressSimple()`：格式化地址信息（简化版本，只需要1个参数）
  - `getSimplifiedAddress()`：获取简化的地址信息

### 2. 员工打卡页面：`pages/home/<USER>/employeeClockIn.uvue`

已更新为使用天地图API获取真实地址信息，替换了原来的模拟地址。

### 3. 测试页面：`pages/test/tiandituTest.uvue`

专门用于测试天地图功能的页面，可以：
- 获取当前位置并解析地址
- 测试指定坐标的地址解析
- 显示详细的解析结果和日志

## 使用方法

### 基本用法

```typescript
import { getAddressFromTianditu, formatAddressSimple, formatAddress, type AddressResult } from '@/utils/tiandituUtils.uts'

// 调用天地图API
getAddressFromTianditu(latitude, longitude, (result: AddressResult): void => {
    if (result.success) {
        // 地址解析成功
        const formattedAddress = formatAddressSimple(result)  // 使用简化版本
        console.log('地址:', formattedAddress)
        console.log('省份:', result.province)
        console.log('城市:', result.city)
        console.log('区县:', result.county)
        console.log('道路:', result.road)
        console.log('POI:', result.poi)
    } else {
        // 地址解析失败
        console.log('错误:', result.errorMessage)
        // 可以使用降级方案显示坐标
        const fallbackAddress = formatAddress(result, true, latitude, longitude)
    }
})
```

### 函数说明

#### 1. formatAddressSimple(result)
简化版本的地址格式化函数，只需要传入地址解析结果。

#### 2. formatAddress(result, includeCoordinates, latitude, longitude)
完整版本的地址格式化函数，支持坐标降级显示。

### 在员工打卡中的应用

员工打卡页面会自动：
1. 获取用户当前位置
2. 调用天地图API解析地址
3. 显示详细的地址信息
4. 如果API调用失败，会降级显示坐标信息

## API特性

### 1. 错误处理

- 网络请求失败时自动降级到坐标显示
- 提供友好的错误提示信息
- 详细的错误日志记录

### 2. 类型安全

- 严格遵循uni-app x的UTS语言规范
- 完整的类型定义和类型检查
- 安全的空值处理

### 3. 地址格式化

- 支持完整地址格式化
- 支持简化地址格式化
- 支持坐标降级显示

## 测试方法

1. 运行项目
2. 导航到测试页面：`/pages/test/tiandituTest`
3. 点击"获取当前位置"测试真实位置解析
4. 点击"测试指定坐标"测试北京天安门坐标解析
5. 查看详细的解析结果和日志信息

## 注意事项

### 1. 网络权限

确保应用有网络访问权限，天地图API需要网络连接。

### 2. 位置权限

获取当前位置需要位置权限，确保用户已授权。

### 3. API限制

天地图API可能有调用频率限制，生产环境中需要注意控制调用频率。

### 4. 坐标系统

天地图使用WGS84坐标系统，与uni.getLocation()的默认坐标系统一致。

## 错误排查

### 1. 地址解析失败

- 检查网络连接
- 检查API密钥是否有效
- 检查坐标是否在有效范围内

### 2. 位置获取失败

- 检查位置权限是否已授权
- 检查GPS是否开启
- 检查设备是否支持定位

### 3. 编译错误

- 确保所有类型导入正确
- 检查UTS语法是否符合规范
- 确保函数调用参数类型正确

## 扩展功能

可以基于现有工具函数扩展更多功能：

1. **地理编码**：根据地址获取坐标
2. **距离计算**：计算两点间距离
3. **地址搜索**：搜索附近的POI
4. **路径规划**：获取导航路径

这些功能可以通过调用天地图的其他API接口实现。
