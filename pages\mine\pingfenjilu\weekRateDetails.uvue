<template>
		<view class="status_bar">
			<!-- 这里是状态栏 -->
		</view>
		<image
		   class="background-image" 
		   src="/static/image/mine_bj.png"
		   mode="cover"
		   
		 />
			
			<view class="nav-bar">
			    
			    <view class="nav-btn" @tap="handleBack">
			      <text class="iconfont icon_font">&#xe696;</text>
			    </view>
			    <text class="title">{{title}}</text>
			
			    <view class="nav-btn"  >
			    </view>
			</view>
			<view class="details_content">
				
				<view class="scoringRules" v-if="rulesDhow">
					<view class="scoringRules_left">
						<text class="iconfont rules_icon">&#xe614;</text>
						<text class="rules_title">得分规则：</text>
					</view>
					<view class="scoringRules_right">
						<text class="rules">本周内的每日清单得分之和×周考主管系数分=周考核得分</text>
					</view>
					<text class="iconfont close_icon" @click="rulesDhow=false">&#xe84d;</text>
				</view>
				<view class="scoring_title">
					<view class="name_gw">
						<text class="name">张某某</text>
						<text class="gw">
							土建工程师
						</text>
					</view>
					<view class="title_content">
						<view class="title_content_left">
							<text class="title_one">2025年1月份第一周考核</text>
							<view class="title_two">
								<text class="font_28">系统打分100*主管系数分</text>
								<text class="lv font_28">1</text>
								<text class="font_28">=100</text>
							</view>
						</view>
						<view class="title_content_right">
							<view class="right_content">
								<text class="right_score">100</text>
								<text class="right_score_title">总得分</text>
							</view>
							
						</view>
					</view>
				</view>
				
					
				
				<scroll-view style="flex:1">
					<view class="details_form_content">
						<view class="list_content" v-for="(item,index) in examineInfo" :key="index">
							<view class="list_item_header">
								<text class="header_left">
									{{item.daytitle}}
								</text>
								<view class="header_right">
									<text class="header_score">得分：{{item.dayScore}}</text>
									<text class="header_showHide" @click="handleShowHide(item)">{{item.showHide==false?"展开":"收起" }}</text>
								</view>
							</view>
							<view class="list_item_content" v-if="item.chlidren!=null&&item.chlidren.length>0&&item.showHide==true">
								<view class="content_item" @click="handleList(sonItem)" v-for="(sonItem,i) in item.chlidren" :key="i">
									<text class="content_item_left">{{sonItem.listTitle}}</text>
									<view class="content_item_right">
										<text class="content_item_score">得分：{{sonItem.listScore}}</text>
										<text class="content_item_you iconfont">&#xe747;</text>
									</view>
								</view>
							</view>
						</view>
						
					</view>	

				</scroll-view>
				
			</view>
	
</template>

<script setup>
	import { examineType,examinechlidrenType  } from '@/utils/apiType.uts'
	
	const title=ref<string>('周考核评分记录详情')
	// 考核id
	const khId=ref<string|number>('')
	// 控制规则打开还是关闭
	const rulesDhow=ref<boolean>(true)
	
	
	// 周考核详情
	const examineInfo=ref<examineType[]>([
		{id:1,daytitle:'2025年1月5日',dayScore:93,showHide:true,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:2,daytitle:'2025年1月6日',dayScore:93,showHide:true,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:3,daytitle:'2025年1月7日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:4,daytitle:'2025年1月8日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:5,daytitle:'2025年1月9日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:6,daytitle:'2025年1月10日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:7,daytitle:'2025年1月11日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		
	])
	
	// 展开收起
	const handleShowHide=(item:examineType)=>{
		if(item.showHide==true){
			item.showHide=false
		}else{
			item.showHide=true
		}
	}
	
	// 跳转清单
	const handleList=(item:examinechlidrenType)=>{
		console.log('跳转清单');
	}
	
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}

	onLoad((option: OnLoadOptions) => {
		// 安全获取属性值
		const id = option['id']
		if (id != null) {
		  khId.value = id as string
		}
	})
</script>

<style lang="scss" scoped>
.page-container{
	width: 100%;
	height: 100%;
}
.background-image {
   width: 100%;
   height: 100%;
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   z-index: -1;
}
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 10rpx;
  // height: 88px;
  // margin-top: 88rpx;
  // padding: 0 32rpx;
  // background-color: #FFFFFF; 
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */
  // position: fixed;
  // top: 0;
  // left: 0;
  // width: 100%;
  // z-index: 99999;
}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title {
  width: 50%;
  text-align: center;
  font-size: 36rpx;
  // font-weight: bold;
  color: #fff; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_font{
	font-size: 32rpx;
	 color: #fff; /* 黑色文字 */
}
.details_content{
	width: 100%;
	flex: 1;
	// margin-top: 88px;
}
.scoringRules{
	width: 100%;
	padding: 20rpx 30rpx;
	background: #FFFFFF;
	margin: 0 auto;
	display: flex;
	flex-direction: row;
	position: relative;
	.scoringRules_left{
		width: 25%;
		display: flex;
		flex-direction: row;
		.rules_icon{
			font-size: 26rpx;
			color:#2a91f1;
		}
		.rules_title{
			font-size: 26rpx;
			color: #131313;
			margin-left: 10rpx;
		}
	}
	.scoringRules_right{
		width: 70%;
		overflow: visible;
		.rules{
			font-size: 26rpx;
			color: #131313;
		}
	}
	.close_icon{
		font-size: 38rpx;
		color: #c6d1db;
		position: absolute;
		right: 20rpx;
		top: 10px;
	}
}
.scoring_title{
	width: 100%;
	margin: 0 auto;
	margin-top: 20rpx;
	padding: 20rpx 40rpx;
	.name_gw{
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;
		overflow: visible;
		
		.name{
			color: #fff;
			font-size: 34rpx;
			font-weight: 700;
			
		}
		.gw{
			font-size: 28rpx;
			padding: 5rpx 15rpx;
			color: #FFB300;
			background: #FFF6D6;
			margin-left: 10rpx;
			border-radius: 10rpx;
		}
	}
	.title_content{
		margin-top: 30rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		.title_content_left{
			width: 70%;
			.title_one{
				width: 100%;
				font-size: 27rpx;
				color: #fff;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.title_two{
				display: flex;
				flex-direction: row;
				// overflow: hidden;
				// text-overflow: ellipsis;
				// white-space: nowrap;
				margin-top: 10rpx;
			}
		}
		.title_content_right{
			width: 30%;
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			justify-content: center;
			.right_content{
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.right_score{
					font-size: 40rpx;
					color: #FFF700;
					font-weight: bold;
				}
				.right_score_title{
					font-size: 28rpx;
					color: #fff;
				}
			}
		}
	}
}
.lv{
	color: #10FF00 !important;
}
.font_28{
	font-size: 28rpx;
	color: #fff;
}
.details_form_content{
	width: 95%;
	margin: 0 auto;
	background: #fff;
	padding: 20rpx;
	border-radius: 10rpx;
	// height: 700px;
}
.list_content{
	width: 100%;
	margin-bottom:10rpx;
	// background: #fff;
	// border-radius: 10rpx;
	// box-shadow: 0rpx 6rpx 18rpx 1rpx rgba(129,165,212,0.16);
	
	.list_item_header{
		background: #F7F8FA;
		padding:20rpx 0;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		// padding-bottom: 0;
		
	}
	.header_left{
		width: 50%;
		font-size:27rpx;
		color:#131313;
		display: flex;
		// align-items:center;
		font-weight: bold;
		margin-left:40rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		line-height:2;
	}
	.header_right{
		width: 40%;
		flex:1;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-around;
		
	}
	.header_score{
		font-size: 27rpx;
		color:#131313;
	}
	.header_showHide{
		padding: 5rpx 20rpx;
		background: #E4F0FF;
		font-size: 26rpx;
		color: #0072E4;
		display: flex;
		border-radius: 20rpx;
	}
	
	.list_item_content{
		width: 90%;
		margin: 0 auto;
		padding: 20rpx 0;
		padding-top:0;
		
		.content_item{
			width: 100%;
			padding: 20rpx 10rpx;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			background: #fff;
			border-bottom:1rpx  solid #e5e5e5;
			
			.content_item_left{
				width: 50%;
				font-size:27rpx;
				color:#2E2E2E;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.content_item_right{
				width: 30%;
				margin-left: 10%;
				flex:1;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				.content_item_score{
					font-size: 27rpx;
					color:#2E2E2E;
				}
				.content_item_you{
					color: #707070;
					font-size: 28rpx;
				}
			}
		}
	}
	
}

</style>
