<template>
	<!-- 触发器 -->
	<view class="datetime-picker-trigger" :class="{ 'disabled': disabled }" @click="handleTriggerClick">
		<text class="trigger-text" :class="{ 'placeholder': displayValue === '' }">
			{{ displayValue === '' ? placeholder : displayValue }}
		</text>
		<text class="iconfont icon_font trigger-icon">&#xe8b5;</text>

		
	</view>

	<!-- 弹窗遮罩 -->
	<view v-if="showPicker" class="picker-mask" @click="handleCancel">
		<view class="picker-container" @click.stop="">
			<!-- 标题栏 -->
			<view class="picker-header">
				<view class="header-btn" @click="handleCancel">
					<text class="btn-text cancel">{{ cancelText }}</text>
				</view>
				<view class="header-title">
					<text class="title-text">{{ getTitle() }}</text>
				</view>
				<view class="header-btn" @click="handleConfirm">
					<text class="btn-text confirm">{{ confirmText }}</text>
				</view>
			</view>

			<!-- 选择器内容 -->
			<picker-view
				class="picker-view"
				:value="pickerValue"
				@change="handlePickerChange"
				:indicator-style="indicatorStyle"
			>
				<!-- 年份列 -->
				<picker-view-column v-if="showYear">
					<view class="picker-item" v-for="(year, index) in years" :key="index">
						<text class="picker-text">{{ year }}年</text>
					</view>
				</picker-view-column>

				<!-- 月份列 -->
				<picker-view-column v-if="showMonth">
					<view class="picker-item" v-for="(month, index) in months" :key="index">
						<text class="picker-text">{{ month }}月</text>
					</view>
				</picker-view-column>

				<!-- 日期列 -->
				<picker-view-column v-if="showDay">
					<view class="picker-item" v-for="(day, index) in days" :key="index">
						<text class="picker-text">{{ day }}日</text>
					</view>
				</picker-view-column>

				<!-- 小时列 -->
				<picker-view-column v-if="showHour">
					<view class="picker-item" v-for="(hour, index) in hours" :key="index">
						<text class="picker-text">{{ hour }}时</text>
					</view>
				</picker-view-column>

				<!-- 分钟列 -->
				<picker-view-column v-if="showMinute">
					<view class="picker-item" v-for="(minute, index) in minutes" :key="index">
						<text class="picker-text">{{ minute }}分</text>
					</view>
				</picker-view-column>

				<!-- 秒钟列 -->
				<picker-view-column v-if="showSecond">
					<view class="picker-item" v-for="(second, index) in seconds" :key="index">
						<text class="picker-text">{{ second }}秒</text>
					</view>
				</picker-view-column>
			</picker-view>
		</view>
	</view>
</template>

<script setup>
	// 组件属性定义
	const props = defineProps({
		// 基础配置
		mode: {
			type: String,
			default: 'datetime'
		},
		value: {
			type: [Date, String],
			default: null
		},
		placeholder: {
			type: String,
			default: '请选择日期时间'
		},
		disabled: {
			type: Boolean,
			default: false
		},
		
		// 日期范围限制
		minDate: {
			type: Date,
			default: () => new Date(1900, 0, 1)
		},
		maxDate: {
			type: Date,
			default: () => new Date(2100, 11, 31)
		},
		
		// 时间精度控制
		showSeconds: {
			type: Boolean,
			default: false
		},
		minuteStep: {
			type: Number,
			default: 1
		},
		
		// 显示格式自定义
		format: {
			type: String,
			default: ''
		},
		
		// 主题样式配置
		theme: {
			type: String,
			default: 'light'
		},
		primaryColor: {
			type: String,
			default: '#1976D2'
		},
		
		// 按钮文本自定义
		confirmText: {
			type: String,
			default: '确定'
		},
		cancelText: {
			type: String,
			default: '取消'
		},
		
		// 表单验证支持
		required: {
			type: Boolean,
			default: false
		},
		validator: {
			type: Function,
			default: null
		}
	})
	
	// 事件定义
	const emit = defineEmits<{
		change: [value: Date]
		confirm: [value: Date]
		cancel: []
		error: [message: string]
	}>()
	
	// 响应式数据
	const showPicker = ref<boolean>(false)
	const currentDate = ref<Date>(new Date())
	const pickerValue = ref<number[]>([])
	
	// 计算属性 - 显示的列
	const showYear = computed(() => props.mode === 'date' || props.mode === 'datetime')
	const showMonth = computed(() => props.mode === 'date' || props.mode === 'datetime')
	const showDay = computed(() => props.mode === 'date' || props.mode === 'datetime')
	const showHour = computed(() => props.mode === 'time' || props.mode === 'datetime')
	const showMinute = computed(() => props.mode === 'time' || props.mode === 'datetime')
	const showSecond = computed(() => (props.mode === 'time' || props.mode === 'datetime') && props.showSeconds)

	// 工具函数 - 必须在使用前定义
	const formatDate = (date: Date, format: string): string => {
		const year = date.getFullYear()
		const month = (date.getMonth() + 1).toString().padStart(2, '0')
		const day = date.getDate().toString().padStart(2, '0')
		const hour = date.getHours().toString().padStart(2, '0')
		const minute = date.getMinutes().toString().padStart(2, '0')
		const second = date.getSeconds().toString().padStart(2, '0')

		return format
			.replace('YYYY', year.toString())
			.replace('MM', month)
			.replace('DD', day)
			.replace('HH', hour)
			.replace('mm', minute)
			.replace('ss', second)
	}

	const getTitle = (): string => {
		switch (props.mode) {
			case 'date': return '选择日期'
			case 'time': return '选择时间'
			case 'datetime': return '选择日期时间'
			default: return '请选择'
		}
	}

	// 计算属性 - 数据源
	const years = computed(() => {
		const startYear = props.minDate.getFullYear()
		const endYear = props.maxDate.getFullYear()
		const result: number[] = []
		for (let i = startYear; i <= endYear; i++) {
			result.push(i)
		}
		console.log('years 计算属性:', '从', startYear, '到', endYear, '共', result.length, '年')
		return result
	})

	const months = computed(() => {
		const result: number[] = []
		for (let i = 1; i <= 12; i++) {
			result.push(i)
		}
		return result
	})

	const days = computed(() => {
		const year = currentDate.value.getFullYear()
		const month = currentDate.value.getMonth() + 1
		const daysInMonth = new Date(year, month, 0).getDate()
		const result: number[] = []

		// 处理日期范围限制
		const minDay = (year === props.minDate.getFullYear() && month === props.minDate.getMonth() + 1)
			? props.minDate.getDate() : 1
		const maxDay = (year === props.maxDate.getFullYear() && month === props.maxDate.getMonth() + 1)
			? Math.min(props.maxDate.getDate(), daysInMonth) : daysInMonth

		for (let i = minDay; i <= maxDay; i++) {
			result.push(i)
		}

		console.log('days 计算属性:', year, '年', month, '月，从', minDay, '到', maxDay, '共', result.length, '天')
		return result
	})

	const hours = computed(() => {
		const result: number[] = []
		for (let i = 0; i <= 23; i++) {
			result.push(i)
		}
		return result
	})

	const minutes = computed(() => {
		const result: number[] = []
		for (let i = 0; i <= 59; i += props.minuteStep) {
			result.push(i)
		}
		return result
	})

	const seconds = computed(() => {
		const result: number[] = []
		for (let i = 0; i <= 59; i++) {
			result.push(i)
		}
		return result
	})



	// 计算属性 - 显示值
	const displayValue = computed(() => {
		if (props.value === null || props.value === '') return ''

		const date = typeof props.value === 'string' ? new Date(props.value) : props.value

		if (props.format !== '') {
			return formatDate(date, props.format)
		}

		// 默认格式
		switch (props.mode) {
			case 'date':
				return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
			case 'time':
				if (props.showSeconds) {
					return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
				}
				return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
			case 'datetime':
				const dateStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
				const timeStr = props.showSeconds
					? `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
					: `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
				return `${dateStr} ${timeStr}`
			default:
				return ''
		}
	})

	// 样式常量
	const indicatorStyle = computed(() => {
		return 'height: 50rpx; border-top: 1rpx solid #e0e0e0; border-bottom: 1rpx solid #e0e0e0;'
	})

	// 工具函数 - 必须在使用前定义
	const validateDate = (date: Date): boolean => {
		if (date < props.minDate || date > props.maxDate) {
			return false
		}

		// 简化 validator 处理，暂时跳过自定义验证
		// if (props.validator !== null && typeof props.validator === 'function') {
		//     return (props.validator as Function)(date)
		// }

		return true
	}

	const initPickerValue = (): void => {
		console.log('=== initPickerValue 开始 ===')

		const date = (props.value !== null && props.value !== '') ? (typeof props.value === 'string' ? new Date(props.value) : props.value) : new Date()
		console.log('初始化日期:', date)
		console.log('日期年份:', date.getFullYear())
		console.log('日期月份:', date.getMonth() + 1)
		console.log('日期日期:', date.getDate())
		console.log('日期小时:', date.getHours())
		console.log('日期分钟:', date.getMinutes())
		console.log('日期秒钟:', date.getSeconds())

		currentDate.value = new Date(date)
		console.log('设置 currentDate.value:', currentDate.value)

		const values: number[] = []

		if (showYear.value) {
			const yearIndex = years.value.indexOf(date.getFullYear())
			console.log('年份数组:', years.value.slice(0, 5), '...(共', years.value.length, '个)')
			console.log('查找年份', date.getFullYear(), '的索引:', yearIndex)
			// 如果找不到，使用默认值 0
			if (yearIndex >= 0) {
				values.push(yearIndex)
			} else {
				values.push(0)
			}
		}
		if (showMonth.value) {
			const monthIndex = months.value.indexOf(date.getMonth() + 1)
			console.log('月份数组:', months.value)
			console.log('查找月份', date.getMonth() + 1, '的索引:', monthIndex)
			if (monthIndex >= 0) {
				values.push(monthIndex)
			} else {
				values.push(0)
			}
		}
		if (showDay.value) {
			const dayIndex = days.value.indexOf(date.getDate())
			console.log('日期数组:', days.value.slice(0, 10), '...(共', days.value.length, '个)')
			console.log('查找日期', date.getDate(), '的索引:', dayIndex)
			if (dayIndex >= 0) {
				values.push(dayIndex)
			} else {
				values.push(0)
			}
		}
		if (showHour.value) {
			const hourIndex = hours.value.indexOf(date.getHours())
			console.log('小时数组长度:', hours.value.length)
			console.log('查找小时', date.getHours(), '的索引:', hourIndex)
			if (hourIndex >= 0) {
				values.push(hourIndex)
			} else {
				values.push(0)
			}
		}
		if (showMinute.value) {
			const minuteIndex = minutes.value.indexOf(date.getMinutes())
			console.log('分钟数组长度:', minutes.value.length)
			console.log('查找分钟', date.getMinutes(), '的索引:', minuteIndex)
			if (minuteIndex >= 0) {
				values.push(minuteIndex)
			} else {
				values.push(0)
			}
		}
		if (showSecond.value) {
			const secondIndex = seconds.value.indexOf(date.getSeconds())
			console.log('秒钟数组长度:', seconds.value.length)
			console.log('查找秒钟', date.getSeconds(), '的索引:', secondIndex)
			if (secondIndex >= 0) {
				values.push(secondIndex)
			} else {
				values.push(0)
			}
		}

		console.log('最终的 values 数组:', values)
		pickerValue.value = values
		console.log('设置 pickerValue.value:', pickerValue.value)
		console.log('=== initPickerValue 结束 ===')
	}

	// 事件处理函数
	const handleTriggerClick = (): void => {
		if (props.disabled) return

		initPickerValue()
		showPicker.value = true
	}

	const handlePickerChange = (e: UniPickerViewChangeEvent): void => {
		console.log('=== handlePickerChange 开始 ===')
		console.log('事件对象:', e)

		const values = e.detail.value as number[]
		console.log('picker选择的值数组:', values)

		// 更新pickerValue
		pickerValue.value = [...values]

		// 根据当前显示的列来解析选择的值
		let columnIndex = 0
		let year = currentDate.value.getFullYear()
		let month = currentDate.value.getMonth() + 1
		let day = currentDate.value.getDate()
		let hour = currentDate.value.getHours()
		let minute = currentDate.value.getMinutes()
		let second = currentDate.value.getSeconds()

		// 解析年份
		if (showYear.value) {
			const yearIndex = values[columnIndex]
			if (yearIndex >= 0 && yearIndex < years.value.length) {
				year = years.value[yearIndex]
				console.log('更新年份:', year)
			}
			columnIndex++
		}

		// 解析月份
		if (showMonth.value) {
			const monthIndex = values[columnIndex]
			if (monthIndex >= 0 && monthIndex < months.value.length) {
				month = months.value[monthIndex]
				console.log('更新月份:', month)
			}
			columnIndex++
		}

		// 解析日期
		if (showDay.value) {
			const dayIndex = values[columnIndex]
			if (dayIndex >= 0 && dayIndex < days.value.length) {
				day = days.value[dayIndex]
				console.log('更新日期:', day)
			}
			columnIndex++
		}

		// 解析小时
		if (showHour.value) {
			const hourIndex = values[columnIndex]
			if (hourIndex >= 0 && hourIndex < hours.value.length) {
				hour = hours.value[hourIndex]
				console.log('更新小时:', hour)
			}
			columnIndex++
		}

		// 解析分钟
		if (showMinute.value) {
			const minuteIndex = values[columnIndex]
			if (minuteIndex >= 0 && minuteIndex < minutes.value.length) {
				minute = minutes.value[minuteIndex]
				console.log('更新分钟:', minute)
			}
			columnIndex++
		}

		// 解析秒钟
		if (showSecond.value) {
			const secondIndex = values[columnIndex]
			if (secondIndex >= 0 && secondIndex < seconds.value.length) {
				second = seconds.value[secondIndex]
				console.log('更新秒钟:', second)
			}
			columnIndex++
		}

		// 创建新的日期对象
		const newDate = new Date(year, month - 1, day, hour, minute, second)
		console.log('创建新日期:', newDate)

		// 验证日期有效性
		if (validateDate(newDate)) {
			currentDate.value = newDate
			console.log('更新currentDate.value:', currentDate.value)
		} else {
			console.log('日期无效，保持原值')
		}

		console.log('=== handlePickerChange 结束 ===')
	}

	const handleConfirm = (): void => {
		if (!validateDate(currentDate.value)) {
			emit('error', '选择的日期时间不在有效范围内')
			return
		}

		emit('confirm', currentDate.value)
		emit('change', currentDate.value)
		showPicker.value = false
	}

	const handleCancel = (): void => {
		emit('cancel')
		showPicker.value = false
	}





	// 组件生命周期
	onMounted(() => {
		initPickerValue()
	})
</script>

<style>
	/* 触发器样式 */
	.datetime-picker-trigger {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 30rpx;
		background-color: #FFFFFF;
		min-height: 80rpx;
		width: 100%;
	}

	.datetime-picker-trigger:active {
		background-color: #f8f8f8;
	}

	.datetime-picker-trigger.disabled {
		background-color: #f5f5f5;
		border-color: #d0d0d0;
		opacity: 0.6;
	}

	.trigger-text {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}

	.trigger-text.placeholder {
		color: #999999;
	}

	.trigger-icon {
		font-size: 36rpx;
		color: #cccccc;
		margin-left: 20rpx;
	}

	/* 弹窗遮罩 */
	.picker-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1000;
		align-items: flex-end;
		justify-content: center;
	}

	/* 弹窗容器 */
	.picker-container {
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 20rpx 20rpx 0 0;
		overflow: hidden;
		position: fixed;
		bottom: 0;
		z-index: 1001;
	}

	/* 标题栏 */
	.picker-header {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background-color: #FFFFFF;
	}

	.header-btn {
		width: 120rpx;
		height: 88rpx;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
	}

	.header-btn:active {
		background-color: #f8f8f8;
	}

	.btn-text {
		font-size: 30rpx;
	}

	.btn-text.cancel {
		color: #666666;
	}

	.btn-text.confirm {
		color: #1976D2;
		font-weight: bold;
	}

	.header-title {
		flex: 1;
		align-items: center;
		justify-content: center;
	}

	.title-text {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}

	/* 选择器 */
	.picker-view {
		width: 100%;
		height: 400rpx;
		background-color: #FFFFFF;
	}

	.picker-item {
		height: 50rpx;
		align-items: center;
		justify-content: center;
	}

	.picker-text {
		font-size: 28rpx;
		color: #333333;
		text-align: center;
		line-height: 50rpx;
	}

	/* 暗黑主题支持 */
	.datetime-picker-trigger-dark {
		background-color: #2d2d2d;
		border-color: #404040;
	}

	.trigger-text-dark {
		color: #ffffff;
	}

	.trigger-text-dark-placeholder {
		color: #888888;
	}

	.picker-container-dark {
		background-color: #2d2d2d;
	}

	.picker-header-dark {
		background-color: #2d2d2d;
		border-bottom-color: #404040;
	}

	.title-text-dark {
		color: #ffffff;
	}

	.picker-view-dark {
		background-color: #2d2d2d;
	}

	.picker-text-dark {
		color: #ffffff;
	}
</style>
