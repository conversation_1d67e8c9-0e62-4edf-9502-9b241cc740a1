<template>
	<view class="status_bar">
		
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title_header">{{type=='week'?"周考核详情":"月考核详情"}}</text>
	
	    <view class="nav-btn"  >
			
		</view>
	</view>
	<image
	   class="background-image" 
	   src="/static/image/mine_bj.png"
	   mode="cover"
	   
	 />
	 
	 
	 <view class="details_content">
		 <view class="scoringRules" v-if="rulesDhow">
		 	<view class="scoringRules_left">
		 		<text class="iconfont rules_icon">&#xe614;</text>
		 		<text class="rules_title">得分规则：</text>
		 	</view>
		 	<view class="scoringRules_right">
		 		<text v-if="type=='week'" class="rules">本周内的每日清单得分之和×周考主管系数分=周考核得分</text>
				<text v-else-if="type=='month'" class="rules">本月内的每周考核得分之和=月考核得分</text>
		 	</view>
		 	<text class="iconfont close_icon" @click="rulesDhow=false">&#xe84d;</text>
		</view>
		<view class="scoring_title" v-if="type=='week'">
			<text class="name">2025年1月份第二周考核</text>
			<view class="title_content">
				<view class="title_content_left">
					<text class="title_one">系统打分：100</text>
					<text class="title_two">系统打分100*主管系数分</text>
				</view>
				<view class="title_content_right">
					<view class="right_content">
						<text class="right_score">100</text>
						<text class="right_score_title">总得分</text>
					</view>
					
				</view>
			</view>
		</view>
		<view class="scoring_title" v-else-if="type=='month'">
			<view class="title_content">
				<view class="title_content_left">
					<text class="monthtitle_one">系统打分：100</text>
					
				</view>
				<view class="title_content_right">
					<view class="right_content">
						<text class="month_score">396</text>
						<text class="month_score_title">总得分</text>
					</view>
					
				</view>
			</view>
		</view>
		<scroll-view style="flex:1">
			<view class="details_form_content">
				<view class="list_content" v-for="(item,index) in detailsInfo" :key="index">
					<view class="list_item_header">
						<text class="header_left">
							{{item.daytitle}}
						</text>
						<view class="header_right">
							<text class="header_score">得分：{{item.dayScore}}</text>
							<text class="header_showHide" @click="handleShowHide(item)">{{item.showHide==false?"展开":"收起" }}</text>
						</view>
					</view>
					<view class="list_item_content" v-if="item.chlidren!=null&&item.chlidren.length>0&&item.showHide==true">
						<view class="content_item" @click="handleList(sonItem)" v-for="(sonItem,i) in item.chlidren" :key="i">
							<text class="content_item_left">{{sonItem.listTitle}}</text>
							<view class="content_item_right">
								<text class="content_item_score">得分：{{sonItem.listScore}}</text>
								<text class="content_item_you iconfont">&#xe747;</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
		
		</scroll-view>
	</view>

	
	
	
	
</template>

<script setup>
	import { examineType,examinechlidrenType  } from '@/utils/apiType.uts'
	// month月考核  还是  week 周考核
	const type=ref<string>('')
	// 控制规则打开还是关闭
	const rulesDhow=ref<boolean>(true)
	
	// 详情
	const  detailsInfo=ref<examineType[]>([])
	
	// 周考核详情
	const weekExamineInfo=ref<examineType[]>([
		{id:1,daytitle:'2025年1月5日',dayScore:93,showHide:true,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:2,daytitle:'2025年1月6日',dayScore:93,showHide:true,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:3,daytitle:'2025年1月7日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:4,daytitle:'2025年1月8日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:5,daytitle:'2025年1月9日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:6,daytitle:'2025年1月10日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		{id:7,daytitle:'2025年1月11日',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'清单名称1',listScore:86,id:1},
				{listTitle:'清单名称1',listScore:86,id:2},
				{listTitle:'清单名称1',listScore:86,id:3},
				{listTitle:'清单名称1',listScore:86,id:4},
				{listTitle:'清单名称1',listScore:86,id:5},
			]
		},
		
	])
	
	
	// 月考核核详情
	const monthExamineInfo=ref<examineType[]>([
		{id:1,daytitle:'2025年1月份第一周考核',dayScore:93,showHide:true,
			chlidren:[
				{listTitle:'2025年1月1日日报',listScore:86,id:1},
				{listTitle:'2025年1月2日日报',listScore:86,id:2},
				{listTitle:'2025年1月3日日报',listScore:86,id:3},
				{listTitle:'2025年1月4日日报',listScore:86,id:4},
				{listTitle:'2025年1月5日日报',listScore:86,id:5},
			]
		},
		{id:2,daytitle:'2025年1月份第一周考核',dayScore:93,showHide:true,
			chlidren:[
				{listTitle:'2025年1月1日日报',listScore:86,id:1},
				{listTitle:'2025年1月2日日报',listScore:86,id:2},
				{listTitle:'2025年1月3日日报',listScore:86,id:3},
				{listTitle:'2025年1月4日日报',listScore:86,id:4},
				{listTitle:'2025年1月5日日报',listScore:86,id:5},
			]
		},
		{id:3,daytitle:'2025年1月份第一周考核',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'2025年1月1日日报',listScore:86,id:1},
				{listTitle:'2025年1月2日日报',listScore:86,id:2},
				{listTitle:'2025年1月3日日报',listScore:86,id:3},
				{listTitle:'2025年1月4日日报',listScore:86,id:4},
				{listTitle:'2025年1月5日日报',listScore:86,id:5},
			]
		},
		{id:4,daytitle:'2025年1月份第一周考核',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'2025年1月1日日报',listScore:86,id:1},
				{listTitle:'2025年1月2日日报',listScore:86,id:2},
				{listTitle:'2025年1月3日日报',listScore:86,id:3},
				{listTitle:'2025年1月4日日报',listScore:86,id:4},
				{listTitle:'2025年1月5日日报',listScore:86,id:5},
			]
		},
		{id:5,daytitle:'2025年1月份第一周考核',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'2025年1月1日日报',listScore:86,id:1},
				{listTitle:'2025年1月2日日报',listScore:86,id:2},
				{listTitle:'2025年1月3日日报',listScore:86,id:3},
				{listTitle:'2025年1月4日日报',listScore:86,id:4},
				{listTitle:'2025年1月5日日报',listScore:86,id:5},
			]
		},
		{id:6,daytitle:'2025年1月份第一周考核',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'2025年1月1日日报',listScore:86,id:1},
				{listTitle:'2025年1月2日日报',listScore:86,id:2},
				{listTitle:'2025年1月3日日报',listScore:86,id:3},
				{listTitle:'2025年1月4日日报',listScore:86,id:4},
				{listTitle:'2025年1月5日日报',listScore:86,id:5},
			]
		},
		{id:7,daytitle:'2025年1月份第一周考核',dayScore:93,showHide:false,
			chlidren:[
				{listTitle:'2025年1月1日日报',listScore:86,id:1},
				{listTitle:'2025年1月2日日报',listScore:86,id:2},
				{listTitle:'2025年1月3日日报',listScore:86,id:3},
				{listTitle:'2025年1月4日日报',listScore:86,id:4},
				{listTitle:'2025年1月5日日报',listScore:86,id:5},
			]
		},
		
	])
	
	// 展开收起
	const handleShowHide=(item:examineType)=>{
		if(item.showHide==true){
			item.showHide=false
		}else{
			item.showHide=true
		}
	}
	
	// 跳转清单
	const handleList=(item:examinechlidrenType)=>{
		// console.log('跳转清单');
		if(type.value=='week'){
			console.log('跳转清单');
		}else if(type.value=='month'){
			console.log('跳转日报');
		}
	}
	
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	onLoad((option: OnLoadOptions) => {
		// 安全获取属性值
		const id = option['type']
		if (id != null) {
		  type.value = id as string
		  if(id=='week'){
		  	detailsInfo.value = weekExamineInfo.value
		  }else{
		  	detailsInfo.value = monthExamineInfo.value
		  }
		}
	})
</script>

<style lang="scss" scoped>
.page-container{
	background-color: #ffffff;
}
.background-image {
   width: 100%;
   height: 100%;
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   z-index: -1;
}
.nav-bar {
  position: relative;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 10rpx;
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */

}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title_header {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  // font-weight: bold;
  color: #fff; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  // max-width: 60%;
}
.right_title{
	font-size: 28rpx;
	color: #fff;
}
.icon_font{
	font-size: 32rpx;
	 color: #fff; /* 黑色文字 */
}
.details_content{
	width: 100%;
	flex: 1;
}

.scoringRules{
	width: 100%;
	padding: 20rpx 30rpx;
	background: #FFFFFF;
	margin: 0 auto;
	display: flex;
	flex-direction: row;
	position: relative;
	.scoringRules_left{
		width: 25%;
		display: flex;
		flex-direction: row;
		.rules_icon{
			font-size: 26rpx;
			color:#2a91f1;
		}
		.rules_title{
			font-size: 26rpx;
			color: #131313;
			margin-left: 10rpx;
		}
	}
	.scoringRules_right{
		width: 70%;
		overflow: visible;
		.rules{
			font-size: 26rpx;
			color: #131313;
		}
	}
	.close_icon{
		font-size: 38rpx;
		color: #c6d1db;
		position: absolute;
		right: 20rpx;
		top: 5px;
	}
}
.scoring_title{
	width: 100%;
	margin: 0 auto;
	margin-top: 20rpx;
	padding: 20rpx 40rpx;
	.name{
		color: #fff;
		font-size: 32rpx;
		font-weight: 700;
		
	}
	.title_content{
		margin-top: 30rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		.title_content_left{
			width: 70%;
			.title_one{
				width: 100%;
				font-size: 27rpx;
				color: #fff;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-bottom:15rpx;
			}
			.monthtitle_one{
				width: 100%;
				font-size: 32rpx;
				font-weight:bold;
				color: #fff;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				// margin-bottom:15rpx;
			}
			.title_two{
				width: 100%;
				font-size: 27rpx;
				color: #fff;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		.title_content_right{
			width: 30%;
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			justify-content: center;
			.right_content{
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.right_score{
					font-size: 40rpx;
					color: #FFF700;
					font-weight: bold;
				}
				.right_score_title{
					font-size: 28rpx;
					color: #fff;
				}
				.month_score{
					font-size: 48rpx;
					color: #FFF700;
					font-weight: bold;
				}
				.month_score_title{
					font-size: 26rpx;
					color: #fff;
				}
			}
		}
	}
}
.details_form_content{
	width: 95%;
	margin: 0 auto;
	background: #fff;
	padding: 20rpx;
	border-radius: 10rpx;
	margin-top:10rpx;
	.list_content{
		width: 100%;
		margin-bottom:10rpx;
		// background: #fff;
		// border-radius: 10rpx;
		// box-shadow: 0rpx 6rpx 18rpx 1rpx rgba(129,165,212,0.16);
		
		.list_item_header{
			background: #F7F8FA;
			padding:20rpx 0;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			// padding-bottom: 0;
			
		}
		.header_left{
			width: 50%;
			font-size:27rpx;
			color:#131313;
			display: flex;
			// align-items:center;
			font-weight: bold;
			margin-left:40rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			line-height:2;
		}
		.header_right{
			width: 40%;
			flex:1;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-around;
			
		}
		.header_score{
			font-size: 27rpx;
			color:#131313;
		}
		.header_showHide{
			padding: 5rpx 20rpx;
			background: #E4F0FF;
			font-size: 26rpx;
			color: #0072E4;
			display: flex;
			border-radius: 20rpx;
		}
		
		.list_item_content{
			width: 90%;
			margin: 0 auto;
			padding: 20rpx 0;
			padding-top:0;
			
			.content_item{
				width: 100%;
				padding: 20rpx 10rpx;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				background: #fff;
				border-bottom:1rpx  solid #e5e5e5;
				
				.content_item_left{
					width: 50%;
					font-size:27rpx;
					color:#2E2E2E;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				.content_item_right{
					width: 30%;
					margin-left: 10%;
					flex:1;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					.content_item_score{
						font-size: 27rpx;
						color:#2E2E2E;
					}
					.content_item_you{
						color: #707070;
						font-size: 28rpx;
					}
				}
			}
		}
		
	}
}
</style>
