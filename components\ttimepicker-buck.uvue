<template>
	<!-- 触发器 -->
	<view class="datetime-picker-trigger" :class="{ 'disabled': disabled, 'datetime-picker-trigger-dark': theme === 'dark' }" @click="handleTriggerClick">
		<text class="trigger-text" :class="{ 'placeholder': displayValue === '', 'trigger-text-dark': theme === 'dark', 'trigger-text-dark-placeholder': theme === 'dark' && displayValue === '' }">
			{{ displayValue === '' ? placeholder : displayValue }}
		</text>
		<text class="trigger-icon">📅</text>
	</view>
	
	<!-- 弹窗遮罩 -->
	<view v-if="showPicker" class="picker-mask" @click="handleCancel">
		<view class="picker-container" :class="{ 'picker-container-dark': theme === 'dark' }" @click.stop="">
			<!-- 标题栏 -->
			<view class="picker-header" :class="{ 'picker-header-dark': theme === 'dark' }">
				<view class="header-btn" @click="handleCancel">
					<text class="btn-text cancel">{{ cancelText }}</text>
				</view>
				<view class="header-title">
					<text class="title-text" :class="{ 'title-text-dark': theme === 'dark' }">{{ getTitle() }}</text>
				</view>
				<view class="header-btn" @click="handleConfirm">
					<text class="btn-text confirm">{{ confirmText }}</text>
				</view>
			</view>
			
			<!-- 选择器内容 -->
			<picker-view
				class="picker-view"
				:class="{ 'picker-view-dark': theme === 'dark' }"
				:value="pickerValue"
				@change="handlePickerChange"
				:indicator-style="indicatorStyle"
			>
				<!-- 年份列 -->
				<picker-view-column v-if="showYear">
					<view class="picker-item" v-for="(year, index) in years" :key="index">
						<text class="picker-text" :class="{ 'picker-text-dark': theme === 'dark' }">{{ year }}年</text>
					</view>
				</picker-view-column>

				<!-- 月份列 -->
				<picker-view-column v-if="showMonth">
					<view class="picker-item" v-for="(month, index) in months" :key="index">
						<text class="picker-text" :class="{ 'picker-text-dark': theme === 'dark' }">{{ month }}月</text>
					</view>
				</picker-view-column>

				<!-- 日期列 -->
				<picker-view-column v-if="showDay">
					<view class="picker-item" v-for="(day, index) in days" :key="index">
						<text class="picker-text" :class="{ 'picker-text-dark': theme === 'dark' }">{{ day }}日</text>
					</view>
				</picker-view-column>

				<!-- 小时列 -->
				<picker-view-column v-if="showHour">
					<view class="picker-item" v-for="(hour, index) in hours" :key="index">
						<text class="picker-text" :class="{ 'picker-text-dark': theme === 'dark' }">{{ hour }}时</text>
					</view>
				</picker-view-column>

				<!-- 分钟列 -->
				<picker-view-column v-if="showMinute">
					<view class="picker-item" v-for="(minute, index) in minutes" :key="index">
						<text class="picker-text" :class="{ 'picker-text-dark': theme === 'dark' }">{{ minute }}分</text>
					</view>
				</picker-view-column>

				<!-- 秒钟列 -->
				<picker-view-column v-if="showSecond">
					<view class="picker-item" v-for="(second, index) in seconds" :key="index">
						<text class="picker-text" :class="{ 'picker-text-dark': theme === 'dark' }">{{ second }}秒</text>
					</view>
				</picker-view-column>
			</picker-view>
		</view>
	</view>
</template>

<script setup>
	// 组件属性定义
	const props = defineProps({
		// 基础配置
		mode: {
			type: String,
			default: 'datetime'
		},
		value: {
			type: [Date, String],
			default: null
		},
		placeholder: {
			type: String,
			default: '请选择日期时间'
		},
		disabled: {
			type: Boolean,
			default: false
		},
		
		// 日期范围限制
		minDate: {
			type: Date,
			default: () => new Date(1900, 0, 1)
		},
		maxDate: {
			type: Date,
			default: () => new Date(2100, 11, 31)
		},
		
		// 时间精度控制
		showSeconds: {
			type: Boolean,
			default: false
		},
		minuteStep: {
			type: Number,
			default: 1
		},
		
		// 显示格式自定义
		format: {
			type: String,
			default: ''
		},
		
		// 主题样式配置
		theme: {
			type: String,
			default: 'light'
		},
		primaryColor: {
			type: String,
			default: '#1976D2'
		},
		
		// 按钮文本自定义
		confirmText: {
			type: String,
			default: '确定'
		},
		cancelText: {
			type: String,
			default: '取消'
		},
		
		// 表单验证支持
		required: {
			type: Boolean,
			default: false
		},
		validator: {
			type: Function,
			default: null
		}
	})
	
	// 事件定义
	const emit = defineEmits<{
		change: [value: Date]
		confirm: [value: Date]
		cancel: []
		error: [message: string]
	}>()
	
	// 响应式数据
	const showPicker = ref<boolean>(false)
	const currentDate = ref<Date>(new Date())
	const pickerValue = ref<number[]>([])
	
	// 计算属性 - 显示的列
	const showYear = computed(() => props.mode === 'date' || props.mode === 'datetime')
	const showMonth = computed(() => props.mode === 'date' || props.mode === 'datetime')
	const showDay = computed(() => props.mode === 'date' || props.mode === 'datetime')
	const showHour = computed(() => props.mode === 'time' || props.mode === 'datetime')
	const showMinute = computed(() => props.mode === 'time' || props.mode === 'datetime')
	const showSecond = computed(() => (props.mode === 'time' || props.mode === 'datetime') && props.showSeconds)

	// 计算属性 - 数据源
	const years = computed(() => {
		const startYear = props.minDate.getFullYear()
		const endYear = props.maxDate.getFullYear()
		const result: number[] = []
		for (let i = startYear; i <= endYear; i++) {
			result.push(i)
		}
		return result
	})

	const months = computed(() => {
		const result: number[] = []
		for (let i = 1; i <= 12; i++) {
			result.push(i)
		}
		return result
	})

	const days = computed(() => {
		const year = currentDate.value.getFullYear()
		const month = currentDate.value.getMonth() + 1
		const daysInMonth = new Date(year, month, 0).getDate()
		const result: number[] = []

		// 处理日期范围限制
		const minDay = (year === props.minDate.getFullYear() && month === props.minDate.getMonth() + 1)
			? props.minDate.getDate() : 1
		const maxDay = (year === props.maxDate.getFullYear() && month === props.maxDate.getMonth() + 1)
			? Math.min(props.maxDate.getDate(), daysInMonth) : daysInMonth

		for (let i = minDay; i <= maxDay; i++) {
			result.push(i)
		}
		return result
	})

	const hours = computed(() => {
		const result: number[] = []
		for (let i = 0; i <= 23; i++) {
			result.push(i)
		}
		return result
	})

	const minutes = computed(() => {
		const result: number[] = []
		for (let i = 0; i <= 59; i += props.minuteStep) {
			result.push(i)
		}
		return result
	})

	const seconds = computed(() => {
		const result: number[] = []
		for (let i = 0; i <= 59; i++) {
			result.push(i)
		}
		return result
	})

	// 工具函数
	const formatDate = (date: Date, format: string): string => {
		const year = date.getFullYear()
		const month = (date.getMonth() + 1).toString().padStart(2, '0')
		const day = date.getDate().toString().padStart(2, '0')
		const hour = date.getHours().toString().padStart(2, '0')
		const minute = date.getMinutes().toString().padStart(2, '0')
		const second = date.getSeconds().toString().padStart(2, '0')

		return format
			.replace('YYYY', year.toString())
			.replace('MM', month)
			.replace('DD', day)
			.replace('HH', hour)
			.replace('mm', minute)
			.replace('ss', second)
	}

	// 计算属性 - 显示值
	const displayValue = computed(() => {
		if (props.value === null || props.value === '') return ''

		const date = typeof props.value === 'string' ? new Date(props.value) : props.value

		if (props.format !== '') {
			return formatDate(date, props.format)
		}

		// 默认格式
		switch (props.mode) {
			case 'date':
				return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
			case 'time':
				if (props.showSeconds) {
					return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
				}
				return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
			case 'datetime':
				const dateStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
				const timeStr = props.showSeconds
					? `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
					: `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
				return `${dateStr} ${timeStr}`
			default:
				return ''
		}
	})

	// 样式常量
	const indicatorStyle = computed(() => {
		const baseStyle = 'height: 50rpx; border-top: 1rpx solid #e0e0e0; border-bottom: 1rpx solid #e0e0e0;'
		if (props.theme === 'dark') {
			return baseStyle.replace('#e0e0e0', '#404040')
		}
		return baseStyle
	})

	// 方法
	const getTitle = (): string => {
		switch (props.mode) {
			case 'date': return '选择日期'
			case 'time': return '选择时间'
			case 'datetime': return '选择日期时间'
			default: return '请选择'
		}
	}



	const validateDate = (date: Date): boolean => {
		if (date < props.minDate || date > props.maxDate) {
			return false
		}

		if (props.validator && typeof props.validator === 'function') {
			return props.validator(date)
		}

		return true
	}

	const initPickerValue = (): void => {
		const date = (props.value !== null && props.value !== '') ? (typeof props.value === 'string' ? new Date(props.value) : props.value) : new Date()
		currentDate.value = new Date(date)

		const values: number[] = []

		if (showYear.value) {
			values.push(years.value.indexOf(date.getFullYear()))
		}
		if (showMonth.value) {
			values.push(months.value.indexOf(date.getMonth() + 1))
		}
		if (showDay.value) {
			values.push(days.value.indexOf(date.getDate()))
		}
		if (showHour.value) {
			values.push(hours.value.indexOf(date.getHours()))
		}
		if (showMinute.value) {
			values.push(minutes.value.indexOf(date.getMinutes()))
		}
		if (showSecond.value) {
			values.push(seconds.value.indexOf(date.getSeconds()))
		}

		pickerValue.value = values
	}

	const handleTriggerClick = (): void => {
		if (props.disabled) return

		initPickerValue()
		showPicker.value = true
	}

	const handlePickerChange = (e: any): void => {
		const values = e.detail.value as number[]
		pickerValue.value = values

		let columnIndex = 0
		let year = currentDate.value.getFullYear()
		let month = currentDate.value.getMonth()
		let day = currentDate.value.getDate()
		let hour = currentDate.value.getHours()
		let minute = currentDate.value.getMinutes()
		let second = currentDate.value.getSeconds()

		if (showYear.value) {
			year = years.value[values[columnIndex]]
			columnIndex++
		}
		if (showMonth.value) {
			month = months.value[values[columnIndex]] - 1
			columnIndex++
		}
		if (showDay.value) {
			day = days.value[values[columnIndex]]
			columnIndex++
		}
		if (showHour.value) {
			hour = hours.value[values[columnIndex]]
			columnIndex++
		}
		if (showMinute.value) {
			minute = minutes.value[values[columnIndex]]
			columnIndex++
		}
		if (showSecond.value) {
			second = seconds.value[values[columnIndex]]
		}

		// 创建新日期并验证有效性
		const newDate = new Date(year, month, day, hour, minute, second)

		// 处理日期溢出（如2月30日会自动调整为3月2日）
		if (newDate.getMonth() !== month) {
			// 如果月份不匹配，说明日期溢出，调整为该月最后一天
			const lastDayOfMonth = new Date(year, month + 1, 0).getDate()
			newDate.setDate(lastDayOfMonth)
		}

		currentDate.value = newDate
	}

	const handleConfirm = (): void => {
		if (!validateDate(currentDate.value)) {
			emit('error', '选择的日期时间不在有效范围内')
			return
		}

		emit('confirm', currentDate.value)
		emit('change', currentDate.value)
		showPicker.value = false
	}

	const handleCancel = (): void => {
		emit('cancel')
		showPicker.value = false
	}

	// 组件生命周期
	onMounted(() => {
		initPickerValue()
	})
</script>

<style>
	/* 触发器样式 */
	.datetime-picker-trigger {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 30rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 8rpx;
		background-color: #FFFFFF;
		min-height: 80rpx;
		width: 100%;
	}

	.datetime-picker-trigger:active {
		background-color: #f8f8f8;
	}

	.datetime-picker-trigger.disabled {
		background-color: #f5f5f5;
		border-color: #d0d0d0;
		opacity: 0.6;
	}

	.trigger-text {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}

	.trigger-text.placeholder {
		color: #999999;
	}

	.trigger-icon {
		font-size: 28rpx;
		color: #cccccc;
		margin-left: 20rpx;
	}

	/* 弹窗遮罩 */
	.picker-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1000;
		align-items: flex-end;
		justify-content: center;
	}

	/* 弹窗容器 */
	.picker-container {
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 20rpx 20rpx 0 0;
		overflow: hidden;
	}

	/* 标题栏 */
	.picker-header {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background-color: #FFFFFF;
	}

	.header-btn {
		width: 120rpx;
		height: 88rpx;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
	}

	.header-btn:active {
		background-color: #f8f8f8;
	}

	.btn-text {
		font-size: 30rpx;
	}

	.btn-text.cancel {
		color: #666666;
	}

	.btn-text.confirm {
		color: #1976D2;
		font-weight: bold;
	}

	.header-title {
		flex: 1;
		align-items: center;
		justify-content: center;
	}

	.title-text {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}

	/* 选择器 */
	.picker-view {
		width: 100%;
		height: 400rpx;
		background-color: #FFFFFF;
	}

	.picker-item {
		height: 50rpx;
		align-items: center;
		justify-content: center;
	}

	.picker-text {
		font-size: 28rpx;
		color: #333333;
		text-align: center;
		line-height: 50rpx;
	}

	/* 暗黑主题支持 */
	.datetime-picker-trigger-dark {
		background-color: #2d2d2d;
		border-color: #404040;
	}

	.trigger-text-dark {
		color: #ffffff;
	}

	.trigger-text-dark-placeholder {
		color: #888888;
	}

	.picker-container-dark {
		background-color: #2d2d2d;
	}

	.picker-header-dark {
		background-color: #2d2d2d;
		border-bottom-color: #404040;
	}

	.title-text-dark {
		color: #ffffff;
	}

	.picker-view-dark {
		background-color: #2d2d2d;
	}

	.picker-text-dark {
		color: #ffffff;
	}
</style>
