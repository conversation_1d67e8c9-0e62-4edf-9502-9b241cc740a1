<template>
	<view class="status_bar">
		<!-- 这里是状态栏 -->
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title">{{title}}</text>
	
	    <view class="nav-btn"  >
			<!-- <text class="iconfont icon_font" >&#xe7e7;</text> -->
	    </view>
		
		
	</view>
	
	<scrollRefresh  :isRefreshing="refreshing"
	  :isLoading="loading"
	  :hasMore="hasMore"
	  @refresh="onRefreshTriggered"
	  @loadMore="onLoadMoreTriggered">
	<view class="toExamine_list">
		<view class="toExamine_list_item" @click="handleDetails(item)"  v-for="(item,index) in listData" :key="index">
			<view class="toExamine_list_item_header">
				<text class="list_item_name">{{item.nickName}}</text>
				<text class="list_item_gw">{{item.post}}</text>
			</view>
			<view class="toExamine_list_item_content">
				<view class="form_view">
					<text class="form_label">所属项目部：</text>
					<text class="form_content">{{item.dept}}</text>
				</view>
				<view class="form_view">
					<text class="form_label">提交时间：</text>
					<text class="form_content">{{item.time}}</text>
				</view>
			</view>
		</view>
		
	</view>
	</scrollRefresh>
	
</template>

<script setup>
	import {staffAdjustType,pageType} from '@/utils/apiType.uts'
	import scrollRefresh from '@/components/scroll-refresh.uvue'
	
	
	// 标题名称
	const title=ref<string>('调整纪录')
	// 列表
	const listData=ref<staffAdjustType[]>([])
	
	
	const refreshing = ref(false)
	const loading = ref(false)
	const hasMore = ref(true)
	
	// 分页
	const pageInfo=ref<pageType>({
		page:1,
		pageSize:15,
		total:20
	})
	
	// 获取数据函数
	const fetchData = () => {
	
		if (loading.value || !hasMore.value) return
		loading.value=true
		setTimeout(() => {
			const newData: staffAdjustType[] = []
			for (let i = 0; i < pageInfo.value.pageSize; i++) {
				 const id = (pageInfo.value.page - 1) * pageInfo.value.pageSize+ i + 1
				 newData.push({
					id:id,
					nickName:'张某某',
					post:'施工员',
					dept:'一二三项目部',
					time:'2020.01.02 14:20:30'
				 })
			}
			listData.value = pageInfo.value.page === 1 
				 ? newData 
				 : [...listData.value, ...newData]
			
			// 数据结束条件（可调整或移除）
			if(listData.value.length>=pageInfo.value.total){
				hasMore.value = false
			}
			// if (pageInfo.value.page >= 5) hasMore.value = false
			
			loading.value = false
			refreshing.value = false
			pageInfo.value.page++
			
		}, 800)
	  
	}
	
	
	// 下拉刷新事件
	const onRefreshTriggered = () => {
	  if (refreshing.value) return
	  
	  refreshing.value = true
	  pageInfo.value.page = 1
	  hasMore.value = true
	  pageInfo.value.pageSize = 15 // 重置为初始值
	  
	  setTimeout(() => {
	    fetchData()
	  }, 1000)
	}
	
	// 上拉加载更多事件
	const onLoadMoreTriggered = () => {
		 if (!hasMore.value || loading.value) return
		 
		 // 每次加载固定数量 (无需修改pageSize)
		 pageInfo.value.pageSize = 15
		 fetchData()
	}
	
	// 跳转详情
	const handleDetails=(item:staffAdjustType)=>{
		uni.navigateTo({
			url:'/pages/mine/tiaozhengjilu/details?id='+item['id']
		})
	}
	
	
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	// 页面加载时初始化数据
	onLoad((options: OnLoadOptions) => {
		
		fetchData()
	})
</script>

<style lang="scss" scoped>
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0rpx;
  background-color: #FFFFFF; 
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */
}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title {
  width: 50%;
  text-align: center;
  font-size: 36rpx;
  // font-weight: bold;
  color: #000000; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_font{
	font-size: 32rpx;
	 color: #000000; /* 黑色文字 */
}

.toExamine_list{
	width: 100%;
	background: #F7F8FA;
	margin-top: 20rpx;
	.toExamine_list_item{
		width: 96%;
		margin: 0 auto;
		padding: 20rpx 40rpx;
		background: #FFFFFF;
		box-shadow: 5rpx 11rpx 17rpx 1rpx rgba(208,223,241,0.23);
		margin-bottom: 30rpx;
		.toExamine_list_item_header{
			display: flex;
			flex-direction: row;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #E3E7EC;
			.list_item_name{
				color:#131313;
				font-size: 30rpx;
				font-weight: bold;
			}
			.list_item_gw{
				margin-left: 10rpx;
				padding: 10rpx 20rpx;
				background: #FFF6D6;
				font-size: 30rpx;
				color: #FFB300;
				border-radius: 10rpx;
			}
		}
		.toExamine_list_item_content{
			margin-top: 50rpx;
			.form_view{
				width: 100%;
				display: flex;
				flex-direction: row;
				margin-bottom: 20rpx;
				
				.form_label{
					font-size: 28rpx;
					color: #8B8B8B;
				}
				.form_content{
					flex: 1;
					font-size: 28rpx;
					color: #131313;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					
				}
			}
		}
	}
}
</style>
