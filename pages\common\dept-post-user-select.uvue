<template>
	<view class="status_bar">
		<!-- 这里是状态栏 -->
	</view>
	<view class="dept-select-container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-left" @click="handleBack">
				<text class="back-icon">‹</text>
			</view>
			<text class="header-title">选择指派人员</text>
			<view class="header-right" @click="handleSearch">
				<text class="search-icon">🔍</text>
			</view>
		</view>

		<!-- 面包屑导航 -->
		<view class="breadcrumb" v-if="breadcrumbPath.length > 0">
			<text class="breadcrumb-item" @click="handleBreadcrumbClick(-1)">首页</text>
			<text class="breadcrumb-separator" v-if="breadcrumbPath.length > 0"> > </text>
			<template v-for="(item, index) in breadcrumbPath" :key="item.id">
				<text class="breadcrumb-item" @click="handleBreadcrumbClick(index)">{{ item.name }}</text>
				<text class="breadcrumb-separator" v-if="index < breadcrumbPath.length - 1"> > </text>
			</template>
		</view>

		<!-- 搜索框 -->
		<view class="search-container" v-if="showSearch">
			<input class="search-input" v-model="searchKeyword" placeholder="请输入搜索关键词" @input="handleSearchInput" />
		</view>

		<!-- 列表内容 -->
		<scroll-view class="list-container" scroll-y="true">
			<view class="list-item"
				v-for="item in listData"
				:key="item.id"
				:class="{ 'selected': isItemSelected(item) }"
				@click="handleItemClick(item)">
				<view class="item-left">
					<view class="checkbox-container" @click.stop="handleCheckboxClick(item)">
						<view class="checkbox" :class="{ 'checked': isItemSelected(item) }">
							<text class="checkbox-icon" v-if="isItemSelected(item)">✓</text>
						</view>
					</view>
					<text class="item-name">{{ item.name }}</text>
				</view>
				<view class="item-right">
					<text class="arrow-icon" v-if="item.isChild === '1'"> 下级</text>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="listData.length === 0 && !loading">
				<text class="empty-text">暂无数据</text>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading-state" v-if="loading">
				<text class="loading-text">加载中...</text>
			</view>
		</scroll-view>

		<!-- 选择统计 -->
		<!-- <view class="selection-summary" v-if="selectedItems.length > 0">
			<text class="summary-text">已选择 {{ selectedItems.length }} 项</text>
			<view class="summary-details">
				<text class="detail-item" v-if="getSelectedCount('1') > 0">部门: {{ getSelectedCount('1') }}</text>
				<text class="detail-item" v-if="getSelectedCount('2') > 0">岗位: {{ getSelectedCount('2') }}</text>
				<text class="detail-item" v-if="getSelectedCount('3') > 0">用户: {{ getSelectedCount('3') }}</text>
			</view>
		</view> -->

		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<view class="btn btn-cancel" @click="handleCancel">
				<text class="btn-text">取消</text>
			</view>
			<view class="btn btn-confirm" :class="{ 'disabled': selectedItems.length === 0 }" @click="handleConfirm">
				<text class="btn-text">确定</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue'
	import { getDeptPostUserList } from '@/common/api.uts'
	import { DeptPostUserItem, DeptPostUserSelectResult, BreadcrumbItem, OrgBosItem, OrgBosResult } from '@/utils/apiType.uts'

	// 页面参数存储
	const pageOptions = ref<UTSJSONObject | null>(null)

	// 响应式数据
	const currentLevel = ref<string>('1') // 当前层级：1=部门, 2=岗位, 3=用户
	const currentParentId = ref<string>('') // 当前父级ID
	const currentDeptId = ref<string>('') // 当前部门ID
	const breadcrumbPath = ref<Array<BreadcrumbItem>>([]) // 面包屑路径
	const listData = ref<Array<DeptPostUserItem>>([]) // 当前列表数据
	const loading = ref<boolean>(false)
	const showSearch = ref<boolean>(false)
	const searchKeyword = ref<string>('')

	// 选择状态管理
	const selectedItems = ref<Array<DeptPostUserItem>>([]) // 已选择的项目
	const orgBosData = ref<Array<OrgBosItem>>([]) // 组织架构数据

	// 加载数据 - 使用模拟数据
	const loadData = function(type: string, parentId: string, deptId: string, name: string): void {
		loading.value = true

		// 模拟异步加载
		setTimeout(() => {
			let mockData: Array<DeptPostUserItem> = []

			if (type === '1') {
				// 模拟部门数据
				mockData = [
					{ id: '1001', name: '技术部', type: '1', parentId: 0, isChild: '1' },
					{ id: '1002', name: '市场部', type: '1', parentId: 0, isChild: '1' },
					{ id: '1003', name: '人事部', type: '1', parentId: 0, isChild: '0' },
					{ id: '1004', name: '财务部', type: '1', parentId: 0, isChild: '0' }
				]
			} else if (type === '2') {
				// 模拟岗位数据
				if (deptId === '1001') {
					mockData = [
						{ id: '2001', name: '前端开发', type: '2', parentId: 1001, isChild: '1' },
						{ id: '2002', name: '后端开发', type: '2', parentId: 1001, isChild: '1' },
						{ id: '2003', name: '测试工程师', type: '2', parentId: 1001, isChild: '1' }
					]
				} else if (deptId === '1002') {
					mockData = [
						{ id: '2004', name: '市场专员', type: '2', parentId: 1002, isChild: '1' },
						{ id: '2005', name: '销售经理', type: '2', parentId: 1002, isChild: '1' }
					]
				}
			} else if (type === '3') {
				// 模拟用户数据
				if (parentId === '2001') {
					mockData = [
						{ id: '3001', name: '张三', type: '3', parentId: 2001, isChild: '0' },
						{ id: '3002', name: '李四', type: '3', parentId: 2001, isChild: '0' },
						{ id: '3003', name: '王五', type: '3', parentId: 2001, isChild: '0' }
					]
				} else if (parentId === '2002') {
					mockData = [
						{ id: '3004', name: '赵六', type: '3', parentId: 2002, isChild: '0' },
						{ id: '3005', name: '钱七', type: '3', parentId: 2002, isChild: '0' }
					]
				} else if (parentId === '2003') {
					mockData = [
						{ id: '3006', name: '孙八', type: '3', parentId: 2003, isChild: '0' }
					]
				} else if (parentId === '2004') {
					mockData = [
						{ id: '3007', name: '周九', type: '3', parentId: 2004, isChild: '0' },
						{ id: '3008', name: '吴十', type: '3', parentId: 2004, isChild: '0' }
					]
				} else if (parentId === '2005') {
					mockData = [
						{ id: '3009', name: '郑十一', type: '3', parentId: 2005, isChild: '0' }
					]
				}
			}

			// 搜索过滤
			if (name.trim() !== '') {
				mockData = mockData.filter(item => item.name.includes(name.trim()))
			}

			listData.value = mockData
			loading.value = false
		}, 500)
	}

	// 根据ID和类型查找模拟数据
	const findMockDataById = function(id: string, type: string): DeptPostUserItem | null {
		// 模拟数据映射
		const mockDataMap = new Map<string, DeptPostUserItem>()

		// 部门数据
		mockDataMap.set('1001', { id: '1001', name: '技术部', type: '1', parentId: 0, isChild: '1' })
		mockDataMap.set('1002', { id: '1002', name: '市场部', type: '1', parentId: 0, isChild: '1' })
		mockDataMap.set('1003', { id: '1003', name: '人事部', type: '1', parentId: 0, isChild: '0' })
		mockDataMap.set('1004', { id: '1004', name: '财务部', type: '1', parentId: 0, isChild: '0' })

		// 岗位数据
		mockDataMap.set('2001', { id: '2001', name: '前端开发', type: '2', parentId: 1001, isChild: '1' })
		mockDataMap.set('2002', { id: '2002', name: '后端开发', type: '2', parentId: 1001, isChild: '1' })
		mockDataMap.set('2003', { id: '2003', name: '测试工程师', type: '2', parentId: 1001, isChild: '1' })
		mockDataMap.set('2004', { id: '2004', name: '市场专员', type: '2', parentId: 1002, isChild: '1' })
		mockDataMap.set('2005', { id: '2005', name: '销售经理', type: '2', parentId: 1002, isChild: '1' })

		// 用户数据
		mockDataMap.set('3001', { id: '3001', name: '张三', type: '3', parentId: 2001, isChild: '0' })
		mockDataMap.set('3002', { id: '3002', name: '李四', type: '3', parentId: 2001, isChild: '0' })
		mockDataMap.set('3003', { id: '3003', name: '王五', type: '3', parentId: 2001, isChild: '0' })
		mockDataMap.set('3004', { id: '3004', name: '赵六', type: '3', parentId: 2002, isChild: '0' })
		mockDataMap.set('3005', { id: '3005', name: '钱七', type: '3', parentId: 2002, isChild: '0' })
		mockDataMap.set('3006', { id: '3006', name: '孙八', type: '3', parentId: 2003, isChild: '0' })
		mockDataMap.set('3007', { id: '3007', name: '周九', type: '3', parentId: 2004, isChild: '0' })
		mockDataMap.set('3008', { id: '3008', name: '吴十', type: '3', parentId: 2004, isChild: '0' })
		mockDataMap.set('3009', { id: '3009', name: '郑十一', type: '3', parentId: 2005, isChild: '0' })

		const item = mockDataMap.get(id)
		if (item != null && item.type === type) {
			return item
		}
		return null
	}

	// 恢复选择状态
	const restoreSelectedItems = function(orgBosArray: Array<any>): void {
		console.log('开始恢复选择状态，orgBos数量:', orgBosArray.length)

		const restoredItems: Array<DeptPostUserItem> = []

		orgBosArray.forEach((orgBosObj: any, index: number) => {
			console.log(`处理第${index + 1}个orgBos:`, orgBosObj)

			// 恢复部门
			const deptId = (orgBosObj as UTSJSONObject).getString('deptId')
			console.log('部门ID:', deptId)
			if (deptId != null && deptId !== '') {
				const deptItem = findMockDataById(deptId, '1')
				if (deptItem != null) {
					restoredItems.push(deptItem)
					console.log('恢复部门:', deptItem.name)
				}
			} else {
				console.log('部门ID为空，跳过部门恢复')
			}

			// 恢复岗位
			const postIds = (orgBosObj as UTSJSONObject).getArray('postIds')
			console.log('岗位IDs:', postIds)
			if (postIds != null) {
				for (let postIndex = 0; postIndex < postIds.length; postIndex++) {
					const postId = postIds[postIndex] as string
					console.log(`处理岗位ID[${postIndex}]:`, postId)
					const postItem = findMockDataById(postId, '2')
					if (postItem != null) {
						restoredItems.push(postItem)
						console.log('恢复岗位:', postItem.name)
					}
				}
			}

			// 恢复用户
			const userIds = (orgBosObj as UTSJSONObject).getArray('userIds')
			console.log('用户IDs:', userIds)
			if (userIds != null) {
				for (let userIndex = 0; userIndex < userIds.length; userIndex++) {
					const userId = userIds[userIndex] as string
					console.log(`处理用户ID[${userIndex}]:`, userId)
					const userItem = findMockDataById(userId, '3')
					if (userItem != null) {
						restoredItems.push(userItem)
						console.log('恢复用户:', userItem.name)
					}
				}
			}
		})

		console.log('恢复的项目总数:', restoredItems.length)
		selectedItems.value = restoredItems

		console.log('选择状态恢复完成')
	}

	// 页面加载时接收参数
	onLoad((options: UTSJSONObject) => {
		console.log('页面参数 options:', options)
		pageOptions.value = options
		console.log('selectedData参数:', options.getString('selectedData'))
	})

	// 页面初始化
	onMounted(() => {
		console.log('页面初始化开始')

		// 获取页面参数
		const selectedDataParam = pageOptions.value != null ? pageOptions.value.getString('selectedData') : null
		console.log('从URL获取的selectedData参数:', selectedDataParam)

		loadData('1', '', '', '') // 默认加载部门列表

		// 如果有传入的选择数据，进行回显
		if (selectedDataParam != null && selectedDataParam !== '') {
			console.log('开始处理回显数据')
			try {
				// 先解码URL参数
				const decodedData = decodeURIComponent(selectedDataParam)
				console.log('解码后的数据:', decodedData)

				const selectedDataObj = JSON.parseObject(decodedData as string)
				console.log('解析后的数据对象:', selectedDataObj)

				if (selectedDataObj != null) {
					const orgBos = selectedDataObj.getArray('orgBos')
					console.log('orgBos数组:', orgBos)

					if (orgBos != null) {
						console.log('接收到选择数据，orgBos长度:', orgBos.length)

						// 实现回显逻辑
						restoreSelectedItems(orgBos as Array<any>)
					} else {
						console.log('orgBos为空')
					}
				} else {
					console.log('selectedDataObj为空')
				}
			} catch (error) {
				console.error('解析选择数据失败:', error)
			}
		} else {
			console.log('没有传入选择数据或数据为空')
		}

		console.log('页面初始化完成')
	})

	// 根据岗位找到对应的部门ID
	const findDeptIdForPost = function(postItem: DeptPostUserItem): string {
		console.log('查找岗位对应的部门ID，岗位:', postItem.name)
		console.log('当前面包屑路径:', breadcrumbPath.value)
		console.log('当前部门ID:', currentDeptId.value)

		// 从面包屑路径中找到部门ID
		for (let i = breadcrumbPath.value.length - 1; i >= 0; i--) {
			if (breadcrumbPath.value[i].type === '1') {
				console.log('从面包屑找到部门ID:', breadcrumbPath.value[i].id)
				return breadcrumbPath.value[i].id
			}
		}
		console.log('面包屑中没有部门，使用currentDeptId:', currentDeptId.value)
		return currentDeptId.value
	}

	// 根据用户找到对应的部门ID
	const findDeptIdForUser = function(userItem: DeptPostUserItem): string {
		console.log('查找用户对应的部门ID，用户:', userItem.name)
		console.log('当前面包屑路径:', breadcrumbPath.value)
		console.log('当前部门ID:', currentDeptId.value)

		// 从面包屑路径中找到部门ID
		for (let i = breadcrumbPath.value.length - 1; i >= 0; i--) {
			if (breadcrumbPath.value[i].type === '1') {
				console.log('从面包屑找到部门ID:', breadcrumbPath.value[i].id)
				return breadcrumbPath.value[i].id
			}
		}
		console.log('面包屑中没有部门，使用currentDeptId:', currentDeptId.value)
		return currentDeptId.value
	}

	// 更新组织架构数据
	const updateOrgBosData = function(): void {
		console.log('=== 开始更新组织架构数据 ===')
		console.log('当前selectedItems:', selectedItems.value)

		const orgBosMap = new Map<string, OrgBosItem>()

		// 首先处理选中的部门
		console.log('第一步：处理选中的部门')
		selectedItems.value.forEach(item => {
			if (item.type === '1') {
				console.log('找到部门:', item.name, item.id)
				// 部门
				orgBosMap.set(item.id, {
					deptId: item.id,
					postIds: [],
					userIds: []
				})
			}
		})
		console.log('部门处理完成，orgBosMap大小:', orgBosMap.size)

		// 处理岗位和用户，无论是否选择了对应部门都要添加
		console.log('第二步：处理岗位和用户')
		selectedItems.value.forEach((item, index) => {
			console.log(`处理第${index + 1}个选择项:`, item.name, item.type, item.id)

			if (item.type === '2') {
				console.log('处理岗位:', item.name)
				// 岗位
				const deptId = findDeptIdForPost(item)
				const deptKey = deptId
				console.log('岗位对应的部门ID:', deptId)

				// 检查是否有选中对应的部门
				const isDeptSelected = selectedItems.value.some(selectedItem =>
					selectedItem.type === '1' && selectedItem.id === deptKey
				)
				console.log('是否选中了对应部门:', isDeptSelected)

				if (isDeptSelected) {
					console.log('有选中对应部门，添加到该部门下')
					// 有选中对应部门，添加到该部门下
					const orgBos = orgBosMap.get(deptKey)
					if (orgBos != null) {
						orgBos.postIds.push(item.id)
						console.log('岗位已添加到部门', deptKey, '，当前岗位列表:', orgBos.postIds)
					}
				} else {
					console.log('没有选中对应部门，创建空部门记录')
					// 没有选中对应部门，创建一个空部门记录
					if (!orgBosMap.has(deptKey)) {
						console.log('创建新的空部门记录，key:', deptKey)
						orgBosMap.set(deptKey, {
							deptId: '',  // 空字符串表示没有选择部门
							postIds: [],
							userIds: []
						})
					}
					const orgBos = orgBosMap.get(deptKey)
					if (orgBos != null) {
						orgBos.postIds.push(item.id)
						console.log('岗位已添加到空部门记录，当前岗位列表:', orgBos.postIds)
					}
				}
			} else if (item.type === '3') {
				console.log('处理用户:', item.name)
				// 用户
				const deptId = findDeptIdForUser(item)
				const deptKey = deptId
				console.log('用户对应的部门ID:', deptId)

				// 检查是否有选中对应的部门
				const isDeptSelected = selectedItems.value.some(selectedItem =>
					selectedItem.type === '1' && selectedItem.id === deptKey
				)
				console.log('是否选中了对应部门:', isDeptSelected)

				if (isDeptSelected) {
					console.log('有选中对应部门，添加到该部门下')
					// 有选中对应部门，添加到该部门下
					const orgBos = orgBosMap.get(deptKey)
					if (orgBos != null) {
						orgBos.userIds.push(item.id)
						console.log('用户已添加到部门', deptKey, '，当前用户列表:', orgBos.userIds)
					}
				} else {
					console.log('没有选中对应部门，创建空部门记录')
					// 没有选中对应部门，创建一个空部门记录
					if (!orgBosMap.has(deptKey)) {
						console.log('创建新的空部门记录，key:', deptKey)
						orgBosMap.set(deptKey, {
							deptId: '',  // 空字符串表示没有选择部门
							postIds: [],
							userIds: []
						})
					}
					const orgBos = orgBosMap.get(deptKey)
					if (orgBos != null) {
						orgBos.userIds.push(item.id)
						console.log('用户已添加到空部门记录，当前用户列表:', orgBos.userIds)
					}
				}
			}
		})

		console.log('第三步：生成最终数组')
		console.log('orgBosMap最终大小:', orgBosMap.size)

		const orgBosArray: Array<OrgBosItem> = []
		orgBosMap.forEach((value, key) => {
			console.log('添加orgBos到数组，key:', key, 'value:', value)
			orgBosArray.push(value)
		})
		orgBosData.value = orgBosArray

		console.log('=== 组织架构数据更新完成 ===')
		console.log('最终orgBosData:', orgBosData.value)
		console.log('orgBosData长度:', orgBosData.value.length)
	}

	// 检查项目是否被选中
	const isItemSelected = function(item: DeptPostUserItem): boolean {
		return selectedItems.value.some(selectedItem => selectedItem.id === item.id)
	}

	// 处理复选框点击
	const handleCheckboxClick = function(item: DeptPostUserItem): void {
		console.log('=== 复选框点击 ===')
		console.log('点击的项目:', item.name, item.type, item.id)

		const index = selectedItems.value.findIndex(selectedItem => selectedItem.id === item.id)
		if (index >= 0) {
			// 已选中，取消选择
			console.log('取消选择:', item.name)
			selectedItems.value.splice(index, 1)
		} else {
			// 未选中，添加选择
			console.log('添加选择:', item.name)
			selectedItems.value.push(item)
		}

		console.log('当前selectedItems长度:', selectedItems.value.length)
		console.log('当前selectedItems:', selectedItems.value.map(i => `${i.name}(${i.type})`))

		updateOrgBosData()
	}

	// 获取项目图标
	const getItemIcon = function(type: string): string {
		switch (type) {
			case '1': return '🏢' // 部门
			case '2': return '💼' // 岗位
			case '3': return '👤' // 用户
			default: return '📁'
		}
	}



	// 获取指定类型的选择数量
	const getSelectedCount = function(type: string): number {
		return selectedItems.value.filter(item => item.type === type).length
	}

	// 处理项目点击
	const handleItemClick = function(item: DeptPostUserItem): void {
		if (item.isChild === '1') {
			// 有下级，进入下一层
			const breadcrumbItem: BreadcrumbItem = {
				id: item.id,
				name: item.name,
				type: item.type
			}
			breadcrumbPath.value.push(breadcrumbItem)
			
			if (item.type === '1') {
				// 部门，加载岗位
				currentLevel.value = '2'
				currentParentId.value = item.id
				currentDeptId.value = item.id
				loadData('2', '', item.id, '')
			} else if (item.type === '2') {
				// 岗位，加载用户
				currentLevel.value = '3'
				currentParentId.value = item.id
				loadData('3', item.id, currentDeptId.value, '')
			}
		}
	}

	// 面包屑导航点击
	const handleBreadcrumbClick = function(index: number): void {
		if (index === -1) {
			// 返回首页
			breadcrumbPath.value = []
			currentLevel.value = '1'
			currentParentId.value = ''
			currentDeptId.value = ''
			loadData('1', '', '', '')
		} else {
			// 返回指定层级
			const targetItem = breadcrumbPath.value[index]
			breadcrumbPath.value = breadcrumbPath.value.slice(0, index + 1)
			
			if (targetItem.type === '1') {
				// 返回部门的岗位列表
				currentLevel.value = '2'
				currentParentId.value = targetItem.id
				currentDeptId.value = targetItem.id
				loadData('2', '', targetItem.id, '')
			} else if (targetItem.type === '2') {
				// 返回岗位的用户列表
				currentLevel.value = '3'
				currentParentId.value = targetItem.id
				loadData('3', targetItem.id, currentDeptId.value, '')
			}
		}
	}

	// 搜索相关
	const handleSearch = function(): void {
		showSearch.value = !showSearch.value
		if (!showSearch.value) {
			searchKeyword.value = ''
			// 重新加载当前层级数据
			loadData(currentLevel.value, currentParentId.value, currentDeptId.value, '')
		}
	}

	const handleSearchInput = function(event: UniInputEvent): void {
		if (searchKeyword.value.trim() === '') {
			loadData(currentLevel.value, currentParentId.value, currentDeptId.value, '')
		} else {
			loadData(currentLevel.value, currentParentId.value, currentDeptId.value, searchKeyword.value.trim())
		}
	}

	// 返回
	const handleBack = function(): void {
		if (breadcrumbPath.value.length > 0) {
			// 有面包屑，返回上一级
			const lastIndex = breadcrumbPath.value.length - 2
			handleBreadcrumbClick(lastIndex)
		} else {
			// 没有面包屑，关闭页面
			uni.navigateBack()
		}
	}

	// 取消
	const handleCancel = function(): void {
		uni.navigateBack()
	}

	// 确认选择
	const handleConfirm = function(): void {
		if (selectedItems.value.length === 0) {
			uni.showToast({
				title: '请至少选择一项',
				icon: 'none'
			})
			return
		}

		// 构建最终结果
		const result: OrgBosResult = {
			orgBos: orgBosData.value
		}

		// 通过uni.$emit发送事件
		uni.$emit('deptPostUserSelected', result)

		uni.navigateBack()
	}
</script>

<style scoped>
	.dept-select-container {
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	/* 顶部导航栏 */
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 100rpx;
		padding: 0 32rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
		position: relative;
		flex-direction: row;
	}

	.header-left {
		display: flex;
		align-items: center;
		min-width: 120rpx;
	}

	.back-icon {
		font-size: 40rpx;
		color: #1890ff;
		margin-right: 12rpx;
		font-weight: bold;
	}

	.back-text {
		font-size: 34rpx;
		color: #1890ff;
		font-weight: bold;
	}

	.header-title {
		font-size: 38rpx;
		font-weight: 700;
		color: #262626;
		text-align: center;
		flex: 1;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}

	.header-right {
		padding: 12rpx;
		min-width: 120rpx;
		display: flex;
		justify-content: flex-end;
	}

	.search-icon {
		font-size: 36rpx;
		color: #1890ff;
	}

	/* 面包屑导航 */
	.breadcrumb {
		padding: 24rpx 32rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		min-height: 60rpx;
		display: flex;
		align-items: center;
		flex-direction: row;
	}

	.breadcrumb-item {
		font-size: 30rpx;
		color: #1890ff;
		font-weight: bold;
	}

	.breadcrumb-separator {
		font-size: 28rpx;
		color: #bfbfbf;
		margin: 0 12rpx;
	}

	/* 搜索框 */
	.search-container {
		padding: 24rpx 32rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.search-input {
		width: 100%;
		height: 80rpx;
		padding: 0 32rpx;
		font-size: 32rpx;
		background-color: #fafafa;
		border-radius: 40rpx;
		border: 2rpx solid #f0f0f0;
		color: #262626;
	}

	/* 列表容器 */
	.list-container {
		flex: 1;
		background-color: #ffffff;
	}

	.list-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx 40rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background-color: #ffffff;
		flex-direction: row;
	}

	.list-item.selected {
		background-color: #f0f9ff;
	}

	.list-item.selected .item-name {
		color: #1677ff;
		font-weight: bold;
	}

	.item-left {
		display: flex;
		align-items: center;
		flex: 1;
		flex-direction: row;
	}

	.checkbox-container {
		margin-right: 24rpx;
		padding: 8rpx;
	}

	.checkbox {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #d9d9d9;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
	}

	.checkbox.checked {
		background-color: #1677ff;
		border-color: #1677ff;
	}

	.checkbox-icon {
		font-size: 24rpx;
		color: #ffffff;
		font-weight: 700;
	}

	.item-name {
		font-size: 32rpx;
		color: #000000;
		flex: 1;
	
	}

	.item-right {
		display: flex;
		align-items: center;
	}

	.arrow-icon {
		font-size: 24rpx;
		color: #1677ff;

	}

	/* 空状态和加载状态 */
	.empty-state,
	.loading-state {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 400rpx;
		margin: 80rpx 0;
	}

	.empty-text,
	.loading-text {
		font-size: 32rpx;
		color: #8c8c8c;
	}

	/* 选择统计 */
	.selection-summary {
		padding: 24rpx 32rpx;
		background-color: #f0f9ff;
		border-top: 1rpx solid #e6f7ff;
		border-bottom: 1rpx solid #e6f7ff;
	}

	.summary-text {
		font-size: 30rpx;
		color: #1677ff;
		font-weight: 700;
		margin-bottom: 12rpx;
	}

	.summary-details {
		display: flex;
		flex-wrap: wrap;
	}

	.detail-item {
		font-size: 26rpx;
		color: #595959;
		margin-right: 32rpx;
		margin-bottom: 8rpx;
	}

	/* 底部按钮 */
	.footer-buttons {
		flex-direction: row;
		padding: 40rpx 30rpx;
		padding-bottom: 60rpx;
		background-color: #ffffff;
		border-top: 1rpx solid #f0f0f0;
	}

	.btn {
		flex: 1;
		height: 88rpx;
		align-items: center;
		justify-content: center;
		border-radius: 44rpx;
		margin: 0 12rpx;
	}

	.btn-cancel {
		background-color: transparent;
		border: 2rpx solid #1976D2;
	}

	.btn-cancel .btn-text {
		color: #1976D2;
		font-size: 32rpx;
		font-weight: bold;
	}

	.btn-confirm {
		background-color: #1976D2;
	}

	.btn-confirm.disabled {
		background-color: #f5f5f5;
		border: 2rpx solid #d9d9d9;
	}

	.btn-confirm.disabled .btn-text {
		color: #cccccc;
	}

	.btn-confirm .btn-text {
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bold;
	}
</style>
