<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="page-container">
			<view class="info_header">
				<image class="title_icon" src="/static/image/grxx.png" mode=""></image>
				<text class="title">个人基本信息</text>
			</view>
			<view class="form_div">
				<text class="form_label">姓名：</text>
				<text class="form_label">{{mineInfo.nickName}}</text>
			</view>
			<view class="form_div">
				<text class="form_label">性别：</text>
				<text class="form_label" v-if="isEdit==1">{{mineInfo.sex=='1'?"男":"女"}}</text>
				<radio-group v-else  @change="radioChange" class="uni-row">
				  <radio :value="1" :checked="mineInfo.sex==1" class="radio">男</radio>
				  <radio :value="2" :checked="mineInfo.sex==2" class="radio">女</radio>
				</radio-group>
			</view>
			<view class="form_div">
				<text class="form_label">年龄：</text>
				<text class="form_label" v-if="isEdit==1">{{mineInfo.age}}</text>
				<input
					v-else
					style="width: 70%;text-align: right;"
					v-model="mineInfo.age"
					placeholder="请输入年龄" 
					placeholder-class="placeholder"
					type="number"
					
				/>
			</view>
			<view class="form_div">
				<text class="form_label">手机号：</text>
				<text class="form_label" v-if="isEdit==1">{{mineInfo.phone}}</text>
				<input
					v-else
					style="width: 70%;text-align: right;"
					v-model="mineInfo.phone"
					placeholder="请输入手机号" 
					placeholder-class="placeholder"
					type="number"
					maxlength="11"	
					
				/>
			</view>
			<view class="form_div">
				<text class="form_label">工号：</text>
				<text class="form_label" v-if="isEdit==1">{{mineInfo.workNum}}</text>
				<input
					v-else
					style="width: 70%;text-align: right;"
					v-model="mineInfo.workNum"
					placeholder="请输入工号" 
					placeholder-class="placeholder"
					
				/>
			</view>
			<view class="form_div">
				<text class="form_label">所属项目部：</text>
				<text class="form_label" v-if="isEdit==1">{{mineInfo.tenantName}}</text>
				<view v-else class="picker-box" @click="hanldeshow(1)">
				   <text v-if="mineInfo.tenantName!=''">{{ mineInfo.tenantName  }}</text>
				   <text class="weiSelect" v-else> 请选择项目部</text>
				 </view>
				
			</view>
			<view class="form_div">
				<text class="form_label">任职部门：</text>
				<text class="form_label" v-if="isEdit==1">{{mineInfo.deptNmae}}</text>
				<!-- <uni-data-picker style="width: 80%;"  placeholder="请选择任职部门" v-model="dept" :localdata="deptList" @change="onchange" ></uni-data-picker> -->
				
				
			</view>
			<selectPicker v-if="showPicker"  :show="showPicker" @close="pickerColse" @confirm="pickerConfirm" :items="items" :option="selectedIndex"></selectPicker>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref } from 'vue'
	import {confirmHcType,ProjectOption} from '@/utils/apiType.uts'
	import selectPicker from '@/components/select_picker.uvue'
	import treeSelect from '@/components/treeSelect.uvue'
	// 是否可以修改 1是不可以修改 2 是可以修改
	const isEdit=ref<number>(1)
	
	// 个人信息
	const mineInfo=ref<UTSJSONObject>({
		nickName:'张无忌',
		sex:1,
		age:32,
		phone:'13466885696',
		workNum:'XMB5896111',
		tenantId:1,
		tenantName:'一二三项目部',
		deptId:2,
		deptNmae:'工程部',
		postId:'',
		postName:'施工员',
		
	})
	// 打开当前第几个弹窗
	const currentSelect=ref<number|string|null>(null)
	// 控制弹窗
	const showPicker=ref(false)  
	// 需要传递的数据
	const items=ref<ProjectOption[]|null>(null)
	const selectedIndex=ref<number[]>([0])  // 选择的是第几个
	// 岗位选择 
	const post=ref<ProjectOption|null>(null)
	// 任职部门
	const dept=ref<UTSJSONObject[]|null>([ {text: "一年级", value: "1"}, {text: "1.1班", value: "2"} ])
	
	// 所属项目部
	const multiTenant=ref<ProjectOption[]>([               // 可选项数据
		{ value: 1, label: '一二三项目部' },
		{ value: 22, label: '选项二' },
		{ value: 33, label: '选项三' },
		{ value: 44, label: '选项四' },
		{ value: 55, label: '选项五' }
	])
	// 任职部门
	const deptList=ref<UTSJSONObject[]>([{
		text: "一年级",
		value: "1",
		children: [{
			text: "1.1班",
			value: "2"
		},
		{
			text: "1.2班",
			value: "3"
		}]
	},
	{
		text: "二年级",
		value: "4",
		children: [{
			text: "2.1班",
			value: "5"
		},
		{
			text: "2.2班",
			value: "6"
		}]
	},
	])
	
	
	// 修改性别
	const radioChange=(e:UniRadioGroupChangeEvent)=>{
		mineInfo.value.sex=parseInt(e.detail.value as string)
	}
	
	//打开弹窗选择
	const hanldeshow=(i:number)=>{
		currentSelect.value=i
		if(i==1){
			if(mineInfo.value.tenantId!=null){
				const index = multiTenant.value.findIndex(item => item.value == mineInfo.value.tenantId);
				
				selectedIndex.value=[index]
			}
			items.value=multiTenant.value
		}
		showPicker.value=true
	}
	
	// 选择弹窗关闭
	const pickerColse=()=>{
		showPicker.value=false
	}
	// 选择弹窗确认
	const pickerConfirm=(data:confirmHcType)=>{
		const jieshou=data.selectedItem as ProjectOption
		if(currentSelect.value==1){
			
			mineInfo.value.tenantId=jieshou.value
			mineInfo.value.tenantName=jieshou.label
			
		}else if(currentSelect.value==2){
			post.value=data.selectedItem
		}
		
		
		
		// selectedProject.value=data.label
		showPicker.value=false
		
	}
	
	// 任职部门选择
	const onchange=(e:UTSJSONObject[])=>{
		console.log(e);
		dept.value=e
		mineInfo.value.deptId=e[e.length-1].value
	}
</script>

<style lang="scss" scoped>
.page-container{
	background-color: #ffffff;
	height: 100%;
}
.info_header{
	width: 100%;
	background: #F1F1F1;
	padding: 30rpx 40rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	.title_icon{
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	.title{
		color: #131313;
		font-size: 28rpx;
	}
}
.form_div{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 40rpx 40rpx;
	border-bottom: 1rpx solid #e5e5e5;
}
.form_label{
	color: #353535;
	font-size:30rpx;
}
.radio{
	margin-right: 20rpx;
}

</style>
