/**
 * 天地图工具类
 * 提供逆地理编码、地理编码等功能
 */

// 天地图API密钥
export const TIANDITU_KEY = '4552ec277a7690bf1e3b23cd1b6b2d63'

// 天地图地址组件类型
export type TiandituAddressComponent = {
	nation: string
	province: string
	city: string
	county: string
	town: string
	road: string
	poi: string
	poi_distance: string
}

// 天地图结果类型
export type TiandituResult = {
	formatted_address: string
	addressComponent: TiandituAddressComponent
}

// 天地图逆地理编码响应类型
export type TiandituGeocodeResponse = {
	status: string
	result: TiandituResult
}

// 地址解析结果类型
export type AddressResult = {
	success: boolean
	address: string
	province: string
	city: string
	county: string
	road: string
	poi: string
	errorMessage: string
}

/**
 * 使用天地图API进行逆地理编码
 * @param latitude 纬度
 * @param longitude 经度
 * @param callback 回调函数
 */
export const getAddressFromTianditu = (
	latitude: number, 
	longitude: number, 
	callback: (result: AddressResult) => void
): void => {
	// 构建天地图逆地理编码API URL
	const postStr = JSON.stringify({
		lon: longitude,
		lat: latitude,
		ver: 1
	})
	
	const tiandituUrl = `https://api.tianditu.gov.cn/geocoder?postStr=${encodeURIComponent(postStr)}&type=geocode&tk=${TIANDITU_KEY}`
	
	uni.request({
		url: tiandituUrl,
		method: 'GET',
		timeout: 10000,
		header: {
			'Content-Type': 'application/json'
		},
		success: (res: any): void => {
			try {
				if (res != null) {
					// 使用UTSJSONObject安全访问响应数据
					const resObj = res as UTSJSONObject
					const data = resObj.get('data')

					if (data != null) {
						const responseData = data as TiandituGeocodeResponse
					
					if (responseData.status == '0' && responseData.result != null) {
						// 解析成功
						const result = responseData.result
						const addressComponent = result.addressComponent
						
						const addressResult: AddressResult = {
							success: true,
							address: result.formatted_address != null ? result.formatted_address : '',
							province: addressComponent != null && addressComponent.province != null ? addressComponent.province : '',
							city: addressComponent != null && addressComponent.city != null ? addressComponent.city : '',
							county: addressComponent != null && addressComponent.county != null ? addressComponent.county : '',
							road: addressComponent != null && addressComponent.road != null ? addressComponent.road : '',
							poi: addressComponent != null && addressComponent.poi != null ? addressComponent.poi : '',
							errorMessage: ''
						}
						
						callback(addressResult)
					} else {
						// API返回错误状态
						const errorResult: AddressResult = {
							success: false,
							address: '',
							province: '',
							city: '',
							county: '',
							road: '',
							poi: '',
							errorMessage: '天地图API返回错误状态'
						}
						callback(errorResult)
					}
				} else {
					// 响应数据为空
					const errorResult: AddressResult = {
						success: false,
						address: '',
						province: '',
						city: '',
						county: '',
						road: '',
						poi: '',
						errorMessage: '响应数据为空'
					}
					callback(errorResult)
				}
			} else {
				// res为空
				const errorResult: AddressResult = {
					success: false,
					address: '',
					province: '',
					city: '',
					county: '',
					road: '',
					poi: '',
					errorMessage: '响应为空'
				}
				callback(errorResult)
			}
		} catch (parseError: any) {
				console.log('天地图API响应解析错误:', parseError)
				const errorResult: AddressResult = {
					success: false,
					address: '',
					province: '',
					city: '',
					county: '',
					road: '',
					poi: '',
					errorMessage: '响应数据解析失败'
				}
				callback(errorResult)
			}
		},
		fail: (error: any): void => {
			console.log('天地图API请求失败:', error)
			const errorResult: AddressResult = {
				success: false,
				address: '',
				province: '',
				city: '',
				county: '',
				road: '',
				poi: '',
				errorMessage: '网络请求失败'
			}
			callback(errorResult)
		}
	})
}

/**
 * 格式化地址信息（简化版本）
 * @param result 地址解析结果
 * @returns 格式化后的地址字符串
 */
export const formatAddressSimple = (result: AddressResult): string => {
	return formatAddress(result, false, 0, 0)
}

/**
 * 格式化地址信息（完整版本）
 * @param result 地址解析结果
 * @param includeCoordinates 是否包含坐标信息
 * @param latitude 纬度
 * @param longitude 经度
 * @returns 格式化后的地址字符串
 */
export const formatAddress = (
	result: AddressResult,
	includeCoordinates: boolean,
	latitude: number,
	longitude: number
): string => {
	// 处理默认值
	const shouldIncludeCoordinates = includeCoordinates != null ? includeCoordinates : false
	const lat = latitude != null ? latitude : 0
	const lng = longitude != null ? longitude : 0
	if (!result.success) {
		if (shouldIncludeCoordinates && lat != 0 && lng != 0) {
			const latStr = lat.toString().substring(0, 8)
			const lngStr = lng.toString().substring(0, 9)
			return `位置坐标(${latStr},${lngStr})`
		}
		return '位置信息获取失败'
	}
	
	// 优先使用详细地址
	if (result.address != null && result.address != '') {
		return result.address
	}
	
	// 如果没有详细地址，则组合地址组件
	let formattedAddress = ''
	if (result.province != '') {
		formattedAddress += result.province
	}
	if (result.city != '') {
		formattedAddress += result.city
	}
	if (result.county != '') {
		formattedAddress += result.county
	}
	if (result.road != '') {
		formattedAddress += result.road
	}
	if (result.poi != '') {
		formattedAddress += `(${result.poi})`
	}
	
	return formattedAddress != '' ? formattedAddress : '位置信息解析失败'
}

/**
 * 获取简化的地址信息（只包含主要部分）
 * @param result 地址解析结果
 * @returns 简化的地址字符串
 */
export const getSimplifiedAddress = (result: AddressResult): string => {
	if (!result.success) {
		return '位置信息获取失败'
	}
	
	let simplifiedAddress = ''
	
	// 优先显示城市和区县
	if (result.city != '') {
		simplifiedAddress += result.city
	}
	if (result.county != '') {
		simplifiedAddress += result.county
	}
	
	// 如果有道路信息，添加道路
	if (result.road != '') {
		simplifiedAddress += result.road
	}
	
	// 如果有POI信息，添加POI
	if (result.poi != '') {
		simplifiedAddress += `(${result.poi})`
	}
	
	return simplifiedAddress != '' ? simplifiedAddress : '位置信息解析失败'
}
