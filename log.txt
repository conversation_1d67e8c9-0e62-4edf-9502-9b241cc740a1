11:40:33.827 首页加载完成 at pages/home/<USER>
11:40:33.871 Invalid prop: type check failed for prop "option". Expected <PERSON><PERSON><PERSON>, got Number with value 0.
11:40:33.878 ⁠‌error: java.lang.ClassCastException: java.lang.Integer cannot be cast to io.dcloud.uts.UTSArray‌
11:40:33.878 at components/select_picker.uvue:81:2
11:40:33.878 79 |   
11:40:33.878 80 |   onLoad(()=>{
11:40:33.878 81 |    selectedIndex.value[0]=props.option[0]
11:40:33.878    |    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
11:40:33.878 82 |   })
11:40:33.878 83 |  </script>⁠
11:40:33.893 首页显示 at pages/home/<USER>
11:40:34.101 进入页面:​/pages/home/<USER>"创建dom元素个数":"52个","耗时":"56ms"},{"排版":"1次","耗时":"24ms"},{"渲染":"2次","耗时":"122ms"},{"跳转页面到onReady总耗时":"276ms"}]
11:40:34.101 背景图片加载成功 at pages/home/<USER>
11:40:43.123 style property `text-align|font-size|font-weight|color` is only supported on `<text>|<button>|<input>|<textarea>`. there is an error on `<view class="modal-title">`.
11:40:44.769 ⁠‌error: java.lang.ClassCastException: java.lang.Integer cannot be cast to io.dcloud.uts.UTSArray‌
11:40:44.769 at components/select_picker.uvue:12:3
11:40:44.769 10 |      </view>
11:40:44.769 11 |     </view>
11:40:44.769 12 |     <picker-view 
11:40:44.769    |     ^^^^^^^^^^^^^
11:40:44.770 13 |       class="picker" 
11:40:44.770 14 |       :value="option" @change="bindChange">⁠
