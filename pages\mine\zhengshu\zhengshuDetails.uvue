<template>
	<view class="status_bar">
		
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title">{{title}}</text>
	
	    <view class="nav-btn" @click="handleSearch" >
			<text class="iconfont icon_font" v-if="isEditOrDetails=='2'">&#xe7e7;</text>
	    </view>
	</view>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
	<!-- <view class="details_content" style="flex: 1;"> -->
		
		
		<view class="card">
			<view class="card_title">
				<image class="card_img" src="/static/image/list_item.png" mode=""></image>
				<text class="card_title_font" v-if="isEditOrDetails=='2'">职业资格证书</text>
				<text class="card_title_font" v-else>证书1</text>
			</view>
			<view class="info-row" v-if="isEditOrDetails=='1'">
				<text class="info-label">证书名称：</text>
				<input
				v-model="info.name"
					style="width: 70%;text-align: right;"
					placeholder="请输入证书名称" 
					placeholder-class="placeholder"
					
				/>
			</view>
			<view class="info-row">
				<text class="info-label">证书编号：</text>
				<text class="info-value" v-if="isEditOrDetails=='2'">36984456998</text>
				<input
					v-else
					v-model="info.zsCode"
					style="width: 70%;text-align: right;"
					placeholder="请输入证书编号" 
					placeholder-class="placeholder"
					
				/>
			</view>
			<view class="info-row">
				<text class="info-label">证件有效期：</text>
				<text class="info-value" v-if="isEditOrDetails=='2'">{{info.zsYxq=='-1'?"终生有效":info.zsYxq}}{{info.zsYxq=='-1'?"":'年'}}</text>
				<input
					v-else
					v-model="info.zsYxq"
					style="width: 70%;text-align: right;"
					placeholder="请输入证件有效期" 
					placeholder-class="placeholder"
					type="number"
				/>
				<!-- <DateTimePicker
					v-else
					mode="datetime"
					:value="startTimeValue"
					placeholder="请选择开始时间"
					@change="handleStartTimeChange"
				/> -->
			</view>
			<view class="info-row" v-if="isEditOrDetails=='2'">
				<text class="info-label">上传时间：</text>
				<text class="info-value" >2021.02.03 12:30:36</text>
			</view>
			<view class="info-row" style="flex-direction: column;">
				<text class="info-label">证书图片：</text>
				<view class="zs_img" v-if="isEditOrDetails=='2'">
					<image  class="zs" src="/static/image/zs_cs.jpg" mode="widthFix"></image>
				</view>
				<view class="zs_img_add" v-else>
					<view class="upload_img" v-for="(item,index) in imgList" :key="index">
						<image @click="handelPreview(item)"  class="zs" :src="item" mode="widthFix"></image>
						<text class="iconfont sc_icon" @click="handleDel(index)">&#xe84d;</text>
					</view>
					<view class="upload_img" @click="handelUploadFile">
						<view class="">
							<text class="iconfont xz_icon">&#xe612;</text>
							<text class="upload_title">附件上传</text>
						</view>
					</view>
				</view>
			</view>
			
		</view>
		<button class="editSubmit-btn" @click="handleEditSubmit">{{btnfont}}</button>
	<!-- </view> -->
		
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import DateTimePicker from '@/components/DateTimePicker.uvue'
	import {  zsOption,zsInforType,InventoryItem } from '@/utils/apiType.uts'
	
	// 标题名称
	const title=ref<string>('添加证书')
	
	// 是 1修改 还是 2详情
	const isEditOrDetails=ref<number|string>('1')
	// 按钮文字
	const btnfont=ref<string>('确认')
	
	// 详细信息
	const info=ref<zsInforType>({
		name:'',
		zsCode:'',
		zsYxq:'',
		zsTime:'',
		imgList:'',
		
	})
	// 图片列表
	const imgList=ref<string[]|null>(null)
	
	const startTimeValue = ref<Date | null|string>(null)
	
	
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	// 搜索
	const handleSearch=()=>{
		console.log('搜索');
	}
	
	// 修改提交
	const handleEditSubmit=()=>{
		if(isEditOrDetails.value=='2'){
			isEditOrDetails.value='1'
			btnfont.value='确认'
			
			info.value={
				name: title.value,
				zsCode: '36984456998',
				zsTime: '2021.02.03 12:30:36',
				zsYxq:-1
			} as zsInforType
			
			
			// imgList.value=''
			title.value='添加证书'
		}else{
			console.log('提交');
		}
		
	}

	
	// 上传文件
	const handelUploadFile=()=>{
		uni.chooseFile({
			count: 9,
			type: 'all',
			success: (res) => {
				console.log(res);
				if (imgList.value === null) {
				    imgList.value = []; // 如果为null，则初始化为空数组
				}
				imgList.value.push(res.tempFiles[0].path)
				// this.tempFilePath = res.tempFiles[0].path;
				// console.log('选择的文件路径：', this.tempFilePath);
				// this.uploadFile();
			},
			fail: (err) => {
				console.error('选择文件失败：', err);
			}
		});
	}
	// 删除
	const handleDel=(index:number)=>{
		if(imgList.value!==null){
			imgList.value.splice(index,1)
		}
	}
	
	// 预览
	const handelPreview=(item:string)=>{
		// var weibu_type=item.substring(item.lastIndexOf(".") + 1)
		var img:string[]=[]
		img.push(item)
		uni.previewImage({
			// 当前需要预览的那张图片
			current:1,
			// urls需要预览图片链接的列表
			urls: img,
			// loop是否可循环预览，默认false
			loop:true,
			// indicator 图片底部为圆点default 顶部数字number 默认不显示none
			indicator:"number"
		})
	}
	
	// 开始时间变化处理
	const handleStartTimeChange = (value: Date): void => {
		console.log('开始时间变化:', value)
		startTimeValue.value = value
	}
	
	// 进入页面接收
	// 修改文件：pages/mine/zhengshu/zhengshuDetails.uvue
	onLoad((option: OnLoadOptions) => {
		if (option != null) {
			// 安全获取属性值
			const titleParam = option['title']
			if (titleParam != null) {
			  title.value = titleParam as string
			}
			const typeParams = option['type']
			if (typeParams != null) {
				if(typeParams=='2'){
					btnfont.value='修改信息'
				}
			  isEditOrDetails.value = typeParams as string
			}
		}
	   
	})
</script>

<style lang="scss" scoped>
.details_content{
	background: #F7F8FA;
	height: 100%;
	// position: relative;
}
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  // height: 88px;
  // margin-top: 88rpx;
  // padding: 0 32rpx;
  background-color: #FFFFFF; 
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */
}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title {
  width: 50%;
  text-align: center;
  font-size: 36rpx;
  // font-weight: bold;
  color: #000000; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_font{
	font-size: 32rpx;
	 color: #000000; /* 黑色文字 */
}

.card{
	width: 95%;
	margin: 0 auto;
	background: #FFFFFF;
	box-shadow: 5rpx 11rpx 17rpx 1rpx rgba(208,223,241,0.23);
	border-radius: 14rpx 14rpx 14rpx 14rpx;
	.card_title{
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 20rpx 30rpx;
	}
	.card_img{
		width: 60rpx;	
		height: 60rpx;
	}
	.card_title_font{
		font-size: 30rpx;
		font-weight: 700;
		color: #131313;
		margin-left: 10rpx;
		
	}
	 .info-row {
	    flex-direction: row;
		padding: 30rpx 30rpx;
	    margin-bottom: 15rpx;
		justify-content: space-between;
		border-bottom: 1rpx solid #eeeeee;
	  }
	  
	  .info-label {
	    width: 200rpx;
	    font-size: 27rpx;
	    color: #131313;
	  }
	  
	  .info-value {
	    color: #333;
		font-weight: 400;
		font-size: 27rpx;
		color: #656565;
	  }
	.zs_img{
		width: 100%;
		margin-top: 30rpx;
		.zs{
			width: 100%;
		}
	}
	.zs_img_add{
		width: 100%;
		margin-top: 30rpx;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		overflow: visible;
		.zs{
			width: 100%;
			
		}
	}
	
}
.editSubmit-btn{
	// position: absolute;
	// bottom: 5%;
	// left: 7.5%;
	width: 85%;
	background-color: #0189F6;
	color: #FFFFFF;
	margin: 0 auto;
	border-radius: 50rpx;
	margin-top: 40rpx;
	height: 90rpx;
	line-height: 90rpx;
	font-size: 32rpx;
	box-shadow: 3rpx 7rpx 12rpx 1rpx rgba(1,137,246,0.24);
	margin-top: 80rpx;
	margin-bottom: 20rpx;
}
.upload_img{
	width: 150rpx;
	height: 150rpx;
	border: 1rpx solid #e5e5e5;
	background: #f7f7f7;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	color: #B8B8B8;
	position: relative;
	margin-right: 10rpx;
	margin-bottom: 20rpx;
	overflow: visible;
}
.upload_title{
	font-size: 26rpx;
}
.xz_icon{
	font-size: 36rpx;
	color: #B8B8B8;
	text-align: center;
}
.sc_icon{
	font-size: 36rpx;
	color: #000;
	position: absolute;
	top: -20rpx;
	right: -10rpx;
}
</style>
