<!-- 文件名: ScrollList.vue -->
<template>
  <scroll-view 
  style="flex: 1;"
    class="scroll-view"
    :refresher-enabled="true"
    :refresher-triggered="isRefreshing"
    :scroll-top="scrollTop"
    :show-scrollbar="showScrollbar"
    upper-threshold="50"
    lower-threshold="1"  
    @refresherrefresh="onRefresh"
    @scrolltolower="onLoadMore" 
    @scroll="onScroll">
    
    <!-- 下拉刷新提示区域 -->
    <view slot="refresher" class="refresher-container">
      <view v-if="isRefreshing" class="loading-spinner">
        <view class="spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <view v-else class="pull-text">
        <text>下拉刷新</text>
      </view>
    </view>
    
    <!-- 内容列表插槽 - 父组件自定义内容 -->
    <slot></slot>
    
    <!-- 加载更多指示器 -->
    <view class="load-more">
      <view v-if="isLoading" class="loading-spinner">
        <view class="spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <text v-else-if="!hasMore" class="no-more">没有更多数据了</text>
      <text v-else class="pull-up">上拉加载更多</text>
    </view>
  </scroll-view>
</template>

<script lang="uts" setup>
import { ref } from 'vue'

// 定义传入属性
const props = defineProps({
  isRefreshing: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits(['refresh', 'loadMore', 'scroll'])

// 内部状态
const scrollTop = ref(0)
const showScrollbar = ref(true)

// 下拉刷新事件
const onRefresh = () => {
  if (props.isRefreshing) return
  emit('refresh')
}

// 上拉加载事件
const onLoadMore = () => {
  if (!props.hasMore || props.isLoading) return
  emit('loadMore')
}

// 滚动事件
const onScroll = (event: UniScrollEvent) => {
  scrollTop.value = event.detail.scrollTop
  emit('scroll', event)
}

// 暴露滚动到顶部方法
const scrollToTop = () => {
  scrollTop.value = 0
}
</script>

<style scoped>
/* 下拉刷新区域 */
.refresher-container {
  height: 100rpx;
  justify-content: center;
  align-items: center;
}

.pull-text {
  height: 100rpx;
  justify-content: center;
  align-items: center;
}

/* 加载更多区域 */
.load-more {
  height: 120rpx;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 40rpx;
  height: 40rpx;
  border-radius: 40rpx;
  border: 4rpx solid rgba(74, 140, 255, 0.3);
  border-top-color: #4a8cff;
  margin-right: 15rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.no-more {
  font-size: 28rpx;
  color: #999;
}

.pull-up {
  font-size: 28rpx;
  color: #666;
}

</style>