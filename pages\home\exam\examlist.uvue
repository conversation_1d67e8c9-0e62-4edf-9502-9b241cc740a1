<template>
	<view class="status_bar">
		<!-- 这里是状态栏 -->
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title">{{type=='week'?"我的周考核":"我的月考核"}}</text>
	
	    <view class="nav-btn" @click="handleSearch" >
			<text class="iconfont icon_font" >&#xe7e7;</text>
	    </view>
	</view>
	<view class="content-container" style="flex:1;">
		<scrollRefresh :isRefreshing="refreshing"
		  :isLoading="loading"
		  :hasMore="hasMore"
		  @refresh="onRefreshTriggered"
		  @loadMore="onLoadMoreTriggered">
		<view class="exam-list">
			<view class="exam-item" v-for="(item, index) in listData" :key="index" @click="handleItemClick(item)">
				<view class="exam-item-left">
					<view class="exam-icon">
						<text class="iconfont icon-calendar">&#xe63e;</text>
					</view>
					<view class="exam-info">
						<text class="exam-title">2025年1月份第一周考核</text>
						<!-- <text class="exam-subtitle">当前考核</text> -->
					</view>
				</view>
				<view class="exam-item-right">
					<text class="exam-score">100</text>
					<text class="exam-score-label">总得分</text>
				</view>
			</view>
		</view>
		</scrollRefresh>
	</view>
	
	
	
	
	
	<view class="mask_view" v-if="popupShow==true">
		<view class="popup_content">
			<!-- <text class="popup_header">搜索</text> -->
			
			<view class="popup_content_ss">
				<view class="search">
					<input
						class="input_view"
						v-model="searchVal"
						style="width: 100%;"
						placeholder="请输入清单名称进行搜索" 
						placeholder-class="placeholder"
					/>
				</view>
				
			</view>
			
			<view class="button-group">
			  <button class="btn cancel" @click="closeModal">取消</button>
			  <button class="btn confirm" @click="handleConfirm">确认</button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {pageType} from '@/utils/apiType.uts'
	import scrollRefresh from '@/components/scroll-refresh.uvue'
	
	// 定义数据结构
	type ListItem = {
	  id: number
	  time?: string,
	  nickName?:string,
	  score?: string|number,
	  title?:string,
	}
	
	const type=ref<string>('')
	// 标题名称
	const title=ref<string>('周考评分记录')
	
	const refreshing = ref(false)
	const loading = ref(false)
	const hasMore = ref(true)
	const listData = ref<ListItem[]>([]) // 您的数据
	// 分页
	const pageInfo=ref<pageType>({
		page:1,
		pageSize:15,
		total:20
	})
	
	// 搜索弹窗
	const popupShow=ref<boolean>(false)
	// 搜索条件
	const searchVal=ref<string>('')
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	
	// 获取数据函数
	const fetchData = () => {
	
		if (loading.value || !hasMore.value) return
		loading.value=true
		setTimeout(() => {
			const newData: ListItem[] = []
			for (let i = 0; i < pageInfo.value.pageSize; i++) {
				 const id = (pageInfo.value.page - 1) * pageInfo.value.pageSize+ i + 1
				 newData.push({
				   id: id,
				   title: '2025年1月份第一周考核',
				   score: 100,
				   time: '2025-01-01',
				   nickName: '孙八'
				 })
			}
			listData.value = pageInfo.value.page === 1 
				 ? newData 
				 : [...listData.value, ...newData]
			
			// 数据结束条件（可调整或移除）
			if(listData.value.length>=pageInfo.value.total){
				hasMore.value = false
			}
			// if (pageInfo.value.page >= 5) hasMore.value = false
			
			loading.value = false
			refreshing.value = false
			pageInfo.value.page++
			
		}, 800)
	  
	}
	// 取消弹窗
	const closeModal=()=>{
		console.log('取消');
		popupShow.value=false
	}
	// 搜索
	const handleSearch=()=>{
		console.log('搜索');
		if(popupShow.value==true){
			pageInfo.value.page = 1
			// fetchData()
			popupShow.value=false
		}else{
			popupShow.value=true
			
		}
	}
	// 弹窗确认
	const handleConfirm=()=>{
		console.log('弹窗确认');
		 pageInfo.value.page = 1
		 // fetchData()
		popupShow.value=false
	}
	
	
	
	// 点击列表项
	const handleItemClick = (item: ListItem) => {
		console.log('点击考核项目:', item)
		// 这里可以跳转到详情页面
		// uni.navigateTo({
		//   url: `/pages/exam/detail?id=${item.id}`
		// })
	}
	
	// 下拉刷新事件
	const onRefreshTriggered = () => {
		if (refreshing.value) return
		  
		refreshing.value = true
		pageInfo.value.page = 1
		hasMore.value = true
		pageInfo.value.pageSize = 15 // 重置为初始值
		  
		setTimeout(() => {
			fetchData()
		}, 1000)
	}
	
	// 上拉加载更多事件
	const onLoadMoreTriggered = () => {
		if (!hasMore.value || loading.value) return
		 
		 // 每次加载固定数量 (无需修改pageSize)
		 // pageInfo.value.pageSize = 15
		fetchData()
	}
	
	
	onLoad((option: OnLoadOptions) => {
		// 安全获取属性值
		const id = option['type']
		if (id != null) {
		  type.value = id as string
		}

		// 初始化数据
		fetchData()
	})
</script>

<style lang="scss" scoped>
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0rpx;
  background-color: #FFFFFF; 
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */
}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title {
  width: 50%;
  text-align: center;
  font-size: 36rpx;
  // font-weight: bold;
  color: #000000; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_font{
	font-size: 32rpx;
	 color: #000000; /* 黑色文字 */
}

.mask_view{
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	justify-content: flex-end;
	.popup_content{
		width: 100%;
		height: 90%;
		// padding: 20rpx 30rpx;
		background: #FFFFFF;
		// margin-top: 44px;
		.popup_content_ss{
			margin-top: 88rpx;
			height: 80%;
		}
		
		
	}
	.search{
		width:90%;
		height: 90%;
		margin:0 auto;
	}
	.input_view{
		background: #F7F8FA;
		border-radius: 35rpx 35rpx 35rpx 35rpx;
		border: 1rpx solid #ECEDF1;
		padding: 10px 30rpx;
	}
	.popup_header{
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		font-weight: 400;
		color: #000;
		margin-bottom: 40rpx;
		padding: 20rpx 30rpx;
		height: 6%;
		
		
	}
	.button-group {
	  display: flex;
	  flex-direction: row;
	  justify-content: space-around;
	  border-radius: 20rpx;
	  // height: 6%;
	  .btn{
		  padding: 0 60rpx;
		  font-size: 30rpx;
		  border-radius: 40rpx;
		  
	  }
	  .cancel{
		  background: #EFEFEF;
	  }
	  .confirm{
		  background: #0189F6;
		  color: #fff;
	  }
	  
	}
	.popup_content{
		height: 80%;
	}
}

/* 内容容器样式 */
.content-container {
	background-color: #F5F5F5;
	padding: 20rpx;
}

/* 考核列表样式 */
.exam-list {
	padding: 0;
}

.exam-item {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 30rpx 24rpx;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.exam-item-left {
	display: flex;
	flex-direction: row;
	align-items: center;
	flex: 1;
}

.exam-icon {
	width: 80rpx;
	height: 80rpx;
	background-color: #E3F2FD;
	border-radius: 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 24rpx;
	.icon-calendar{
		font-size: 36rpx;
		color: #2196F3;
	}
}


.exam-info {
	// display: flex;
	// flex-direction: column;
	// justify-content: center;
}

.exam-title {
	font-size: 32rpx;
	font-weight: 400;
	color: #333333;
	line-height: 44rpx;
	margin-bottom: 8rpx;
}

.exam-subtitle {
	padding: 5rpx 10rpx;
	font-size: 24rpx;
	color: #10BB27;
	// line-height: 32rpx;
	background: #E5FFE6;
	width: auto;
	align-self: flex-start;
	border-radius: 8rpx;
}

.exam-item-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	justify-content: center;
}

.exam-score {
	font-size: 48rpx;
	font-weight: bold;
	color: #4CAF50;
	line-height: 56rpx;
	margin-bottom: 4rpx;
}

.exam-score-label {
	font-size: 24rpx;
	color: #999999;
	line-height: 32rpx;
}


</style>
