<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
	<view class="test-container">
		<view class="test-header">
			<text class="test-title">天地图API测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">当前位置信息</text>
			<view class="location-info">
				<text class="info-label">状态：</text>
				<text class="info-value" :class="{ 'loading': locationLoading, 'error': locationError }">
					{{ locationStatus }}
				</text>
			</view>
			<view class="location-info" v-if="!locationLoading && !locationError">
				<text class="info-label">坐标：</text>
				<text class="info-value">{{ coordinates }}</text>
			</view>
			<view class="location-info" v-if="addressResult != null">
				<text class="info-label">详细地址：</text>
				<text class="info-value">{{ addressResult.address }}</text>
			</view>
			<view class="location-info" v-if="addressResult != null">
				<text class="info-label">省份：</text>
				<text class="info-value">{{ addressResult.province }}</text>
			</view>
			<view class="location-info" v-if="addressResult != null">
				<text class="info-label">城市：</text>
				<text class="info-value">{{ addressResult.city }}</text>
			</view>
			<view class="location-info" v-if="addressResult != null">
				<text class="info-label">区县：</text>
				<text class="info-value">{{ addressResult.county }}</text>
			</view>
			<view class="location-info" v-if="addressResult != null">
				<text class="info-label">道路：</text>
				<text class="info-value">{{ addressResult.road }}</text>
			</view>
			<view class="location-info" v-if="addressResult != null">
				<text class="info-label">POI：</text>
				<text class="info-value">{{ addressResult.poi }}</text>
			</view>
		</view>
		
		<view class="test-actions">
			<view class="action-button" @click="getCurrentLocation">
				<text class="button-text">获取当前位置</text>
			</view>
			<view class="action-button secondary" @click="testSpecificLocation">
				<text class="button-text">测试指定坐标</text>
			</view>
		</view>
		
		<view class="test-logs" v-if="logs.length > 0">
			<text class="section-title">测试日志</text>
			<view class="log-item" v-for="(log, index) in logs" :key="index">
				<text class="log-text">{{ log }}</text>
			</view>
		</view>
	</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, onMounted } from 'vue'
	import { getAddressFromTianditu, formatAddress, getSimplifiedAddress, type AddressResult } from '@/utils/tiandituUtils.uts'

	// Reactive data
	const locationStatus = ref<string>('未开始')
	const locationLoading = ref<boolean>(false)
	const locationError = ref<boolean>(false)
	const coordinates = ref<string>('')
	const addressResult = ref<AddressResult | null>(null)
	const logs = ref<string[]>([])

	// 添加日志
	const addLog = (message: string): void => {
		const timestamp = new Date().toLocaleTimeString()
		logs.value.push(`[${timestamp}] ${message}`)
	}

	// 获取当前位置
	const getCurrentLocation = (): void => {
		locationLoading.value = true
		locationError.value = false
		locationStatus.value = '正在获取位置...'
		addressResult.value = null
		coordinates.value = ''
		
		addLog('开始获取当前位置')
		
		uni.getLocation({
			type: 'wgs84',
			altitude: true,
			success: (res: any): void => {
				try {
					const resStr = JSON.stringify(res)
					const resData = JSON.parse(resStr) as UTSJSONObject
					const latitude = resData.getNumber('latitude')
					const longitude = resData.getNumber('longitude')

					if (latitude != null && longitude != null) {
						coordinates.value = `${latitude.toString().substring(0, 8)}, ${longitude.toString().substring(0, 9)}`
						addLog(`获取到坐标: ${coordinates.value}`)
						
						// 调用天地图API解析地址
						getAddressFromTianditu(latitude, longitude, (result: AddressResult): void => {
							addressResult.value = result
							
							if (result.success) {
								locationStatus.value = '地址解析成功'
								locationLoading.value = false
								locationError.value = false
								
								addLog('天地图API调用成功')
								addLog(`解析地址: ${result.address}`)
								addLog(`格式化地址: ${formatAddress(result)}`)
								addLog(`简化地址: ${getSimplifiedAddress(result)}`)
							} else {
								locationStatus.value = `地址解析失败: ${result.errorMessage}`
								locationLoading.value = false
								locationError.value = true
								
								addLog(`天地图API调用失败: ${result.errorMessage}`)
							}
						})
					} else {
						locationStatus.value = '位置坐标获取失败'
						locationLoading.value = false
						locationError.value = true
						addLog('位置坐标获取失败')
					}
				} catch (parseError: any) {
					locationStatus.value = '位置数据解析失败'
					locationLoading.value = false
					locationError.value = true
					addLog(`位置数据解析失败: ${parseError}`)
				}
			},
			fail: (error: any): void => {
				locationStatus.value = '位置获取失败'
				locationLoading.value = false
				locationError.value = true
				addLog(`位置获取失败: ${JSON.stringify(error)}`)
			}
		})
	}

	// 测试指定坐标（北京天安门）
	const testSpecificLocation = (): void => {
		locationLoading.value = true
		locationError.value = false
		locationStatus.value = '正在解析指定坐标...'
		addressResult.value = null
		
		const testLatitude = 39.908823
		const testLongitude = 116.397470
		coordinates.value = `${testLatitude}, ${testLongitude}`
		
		addLog(`测试指定坐标: ${coordinates.value} (北京天安门)`)
		
		getAddressFromTianditu(testLatitude, testLongitude, (result: AddressResult): void => {
			addressResult.value = result
			
			if (result.success) {
				locationStatus.value = '地址解析成功'
				locationLoading.value = false
				locationError.value = false
				
				addLog('指定坐标解析成功')
				addLog(`解析地址: ${result.address}`)
				addLog(`格式化地址: ${formatAddress(result)}`)
				addLog(`简化地址: ${getSimplifiedAddress(result)}`)
			} else {
				locationStatus.value = `地址解析失败: ${result.errorMessage}`
				locationLoading.value = false
				locationError.value = true
				
				addLog(`指定坐标解析失败: ${result.errorMessage}`)
			}
		})
	}

	onMounted((): void => {
		addLog('天地图测试页面已加载')
	})
</script>

<style lang="scss" scoped>
.test-container {
	display: flex;
	flex-direction: column;
	padding: 40rpx;
	background-color: #f5f5f5;
}

.test-header {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	display: flex;
	justify-content: center;
}

.test-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.test-section {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 24rpx;
}

.location-info {
	display: flex;
	flex-direction: row;
	margin-bottom: 16rpx;
}

.info-label {
	font-size: 28rpx;
	color: #666666;
	width: 160rpx;
}

.info-value {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
}

.info-value.loading {
	color: #2196f3;
}

.info-value.error {
	color: #ff5722;
}

.test-actions {
	display: flex;
	flex-direction: column;
	margin-bottom: 32rpx;
}

.action-button {
	background-color: #2196f3;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.action-button.secondary {
	background-color: #ff9500;
}

.button-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: bold;
}

.test-logs {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
}

.log-item {
	margin-bottom: 12rpx;
}

.log-text {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.5;
}
</style>
